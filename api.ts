interface LoginResponse {
  token: string;
  user: {
    id: number;
    username: string;
  };
}

interface ApiNote {
  id: number;
  folderId: number | null;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  size: string;
}

interface ApiFolder {
  id: number;
  name: string;
  parentId: number | null;
  createdAt: string;
  updatedAt: string;
}

class ApiService {
  private baseUrl: string;
  private token: string | null;

  constructor() {
    // 动态获取API基础URL
    this.baseUrl = this.getApiBaseUrl();
    this.token = localStorage.getItem('token');
    console.log('API Base URL:', this.baseUrl); // 调试信息
  }

  private getApiBaseUrl(): string {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;

    // 开发环境检测
    const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1';

    // 局域网IP检测（手机访问电脑的情况）
    const isLAN = hostname.startsWith('192.168.') ||
                  hostname.startsWith('10.') ||
                  hostname.startsWith('172.');

    if (isDevelopment) {
      // 本地开发环境
      return 'http://localhost:3001/api';
    } else if (isLAN) {
      // 局域网访问（手机访问电脑）
      return `${protocol}//${hostname}:3001/api`;
    } else {
      // 生产环境使用相对路径，通过Nginx代理
      return `${protocol}//${window.location.host}/api`;
    }
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  setToken(token: string): void {
    this.token = token;
    localStorage.setItem('token', token);
  }

  clearToken(): void {
    this.token = null;
    localStorage.removeItem('token');
  }

  getToken(): string | null {
    return this.token;
  }

  getBaseUrl(): string {
    return this.baseUrl;
  }

  async login(username: string, password: string): Promise<LoginResponse> {
    const url = `${this.baseUrl}/auth/login`;
    console.log('登录请求URL:', url); // 调试信息

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ username, password }),
      });

      console.log('登录响应状态:', response.status); // 调试信息

      if (!response.ok) {
        let errorMessage = '登录失败';
        try {
          const error = await response.json();
          errorMessage = error.message || errorMessage;
        } catch (e) {
          // 如果响应不是JSON格式，使用状态文本
          errorMessage = response.statusText || errorMessage;
        }
        console.error('登录失败:', errorMessage); // 调试信息
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('登录成功:', data.user?.username); // 调试信息
      this.setToken(data.token);
      return data;
    } catch (error) {
      console.error('登录请求异常:', error); // 调试信息
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('网络连接失败，请检查网络或服务器状态');
      }
      throw error;
    }
  }

  async verifyToken(): Promise<{ user: { id: number; username: string } }> {
    if (!this.token) {
      throw new Error('未登录');
    }

    const response = await fetch(`${this.baseUrl}/auth/verify`, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      this.clearToken();
      throw new Error('会话已过期，请重新登录');
    }

    return await response.json();
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<{ message: string }> {
    if (!this.token) {
      throw new Error('未登录');
    }

    const response = await fetch(`${this.baseUrl}/auth/change-password`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify({ currentPassword, newPassword }),
    });

    if (!response.ok) {
      let errorMessage = '修改密码失败';
      try {
        const error = await response.json();
        errorMessage = error.message || errorMessage;
      } catch (e) {
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }

    return await response.json();
  }

  // 笔记相关API
  async getNotes(folderId?: number): Promise<ApiNote[]> {
    const url = folderId
      ? `${this.baseUrl}/notes?folderId=${folderId}`
      : `${this.baseUrl}/notes`;

    const response = await fetch(url, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error('获取笔记失败');
    }

    return await response.json();
  }

  async getNote(id: number): Promise<ApiNote> {
    const response = await fetch(`${this.baseUrl}/notes/${id}`, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error('获取笔记失败');
    }

    return await response.json();
  }

  async createNote(note: { title: string; content: string; folderId?: number }): Promise<ApiNote> {
    const response = await fetch(`${this.baseUrl}/notes`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(note),
    });

    if (!response.ok) {
      throw new Error('创建笔记失败');
    }

    return await response.json();
  }

  async updateNote(id: number, note: { title?: string; content?: string; folderId?: number }): Promise<ApiNote> {
    const response = await fetch(`${this.baseUrl}/notes/${id}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(note),
    });

    if (!response.ok) {
      throw new Error('更新笔记失败');
    }

    return await response.json();
  }

  async deleteNote(id: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/notes/${id}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error('删除笔记失败');
    }
  }

  // 文件夹相关API
  async getFolders(): Promise<ApiFolder[]> {
    const response = await fetch(`${this.baseUrl}/folders`, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error('获取文件夹失败');
    }

    return await response.json();
  }

  async createFolder(folder: { name: string; parentId?: number }): Promise<ApiFolder> {
    const response = await fetch(`${this.baseUrl}/folders`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(folder),
    });

    if (!response.ok) {
      throw new Error('创建文件夹失败');
    }

    return await response.json();
  }

  async updateFolder(id: number, folder: { name?: string; parentId?: number }): Promise<ApiFolder> {
    const response = await fetch(`${this.baseUrl}/folders/${id}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(folder),
    });

    if (!response.ok) {
      throw new Error('更新文件夹失败');
    }

    return await response.json();
  }

  async deleteFolder(id: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/folders/${id}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error('删除文件夹失败');
    }
  }
}

export const api = new ApiService();
export default api; 