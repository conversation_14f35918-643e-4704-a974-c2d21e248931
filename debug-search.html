<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .step { margin: 10px 0; padding: 10px; background: white; border-left: 4px solid #007cba; }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; font-size: 12px; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; background: #007cba; color: white; border: none; border-radius: 3px; }
        input { padding: 8px; width: 300px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .log { max-height: 300px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔍 搜索功能调试页面</h1>
    
    <div class="debug-section">
        <h2>调试步骤</h2>
        
        <div class="step">
            <h3>1. 检查前端搜索触发</h3>
            <p>在主应用中：</p>
            <ol>
                <li>点击"智能知识库"按钮打开面板</li>
                <li>在搜索框中输入"JVM"</li>
                <li>按Enter键触发搜索</li>
                <li>打开浏览器开发者工具查看Console日志</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>2. 预期的Console日志输出</h3>
            <pre>Search response: {success: true, data: {...}}
Search results: [{noteId: 42, title: "JVM", ...}, ...]
Parsed search results: [{...}, ...]
Search results length: 4
Current state before update: {knowledgeSearchResults: [], knowledgeSearchLoading: true}
State updated, calling render...
renderKnowledgeSearchResults called with: {loading: false, resultsLength: 4, results: [...]}
Rendering search results: 4 items
Rendering result 0: {noteId: 42, title: "JVM", ...}
...</pre>
        </div>
        
        <div class="step">
            <h3>3. 可能的问题点</h3>
            <ul>
                <li><strong>网络请求失败</strong>: 检查Network标签页是否有失败的请求</li>
                <li><strong>认证问题</strong>: 检查是否有401/403错误</li>
                <li><strong>数据解析错误</strong>: 检查result.data?.results是否为空</li>
                <li><strong>状态更新失败</strong>: 检查setState是否正确执行</li>
                <li><strong>渲染问题</strong>: 检查render()是否被调用</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>4. 手动测试API</h3>
            <p>在浏览器Console中执行以下代码测试API：</p>
            <pre>// 测试API连接
fetch('/api/knowledge/search', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token')
    },
    body: JSON.stringify({
        query: 'JVM',
        searchType: 'semantic',
        options: { limit: 20, threshold: 0.7, includeContent: false }
    })
})
.then(response => {
    console.log('Response status:', response.status);
    return response.json();
})
.then(data => {
    console.log('API Response:', data);
    console.log('Results count:', data.data?.results?.length || 0);
})
.catch(error => console.error('API Error:', error));</pre>
        </div>
        
        <div class="step">
            <h3>5. 检查DOM更新</h3>
            <p>在Console中检查搜索结果容器：</p>
            <pre>// 检查搜索结果容器
const resultsContainer = document.querySelector('.knowledge-results');
console.log('Results container:', resultsContainer);
console.log('Container innerHTML:', resultsContainer?.innerHTML);

// 检查是否有搜索结果项
const resultItems = document.querySelectorAll('.knowledge-result-item');
console.log('Result items count:', resultItems.length);</pre>
        </div>
        
        <div class="step warning">
            <h3>⚠️ 常见问题排查</h3>
            <ul>
                <li><strong>空白结果</strong>: 检查CSS样式是否隐藏了结果</li>
                <li><strong>加载状态卡住</strong>: 检查knowledgeSearchLoading状态</li>
                <li><strong>搜索不触发</strong>: 检查Enter键事件绑定</li>
                <li><strong>数据格式错误</strong>: 检查后端返回的数据结构</li>
            </ul>
        </div>
        
        <div class="step success">
            <h3>✅ 修复确认</h3>
            <p>如果看到以下内容，说明修复成功：</p>
            <ul>
                <li>Console显示"找到4个结果"</li>
                <li>页面显示4个JVM相关的搜索结果</li>
                <li>每个结果显示标题、相似度、匹配文本等信息</li>
                <li>点击结果可以跳转到对应笔记</li>
            </ul>
        </div>
    </div>
    
    <div class="debug-section">
        <h2>实时日志监控</h2>
        <p>打开主应用，执行搜索，然后查看这里的日志输出：</p>
        <div id="logContainer" class="log">
            等待日志输出...
        </div>
        <button onclick="clearLogs()">清空日志</button>
        <button onclick="startLogCapture()">开始捕获日志</button>
    </div>

    <script>
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let logContainer = document.getElementById('logContainer');
        let capturing = false;
        
        function addLog(type, message) {
            if (!capturing) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type}: ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function startLogCapture() {
            capturing = true;
            logContainer.innerHTML = '开始捕获日志...\n';
            
            // 重写console方法来捕获日志
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                addLog('LOG', args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' '));
            };
            
            console.error = function(...args) {
                originalConsoleError.apply(console, args);
                addLog('ERROR', args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' '));
            };
        }
        
        function clearLogs() {
            logContainer.innerHTML = '日志已清空\n';
        }
        
        // 页面加载时自动开始捕获
        document.addEventListener('DOMContentLoaded', function() {
            console.log('调试页面已加载，准备监控搜索功能');
        });
    </script>
</body>
</html>
