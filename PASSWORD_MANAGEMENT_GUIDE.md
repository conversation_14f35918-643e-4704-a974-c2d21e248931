# 密码管理功能使用指南

## 功能概述

本次更新为笔记应用添加了完整的用户密码管理功能，用户可以安全地修改自己的登录密码。

## 新增功能

### 1. 密码修改功能
- 用户可以在应用内修改自己的登录密码
- 支持密码强度验证（最少6位字符）
- 需要验证当前密码才能设置新密码
- 支持新密码确认输入，防止输入错误

### 2. 用户界面改进
- 在侧边栏用户资料区域添加了设置按钮
- 新增密码修改对话框，界面简洁美观
- 支持键盘快捷键操作（Enter提交，Escape取消）

## 使用方法

### 修改密码步骤：

1. **打开设置**
   - 登录应用后，在左侧边栏找到用户资料区域
   - 点击用户名旁边的设置图标（齿轮图标）

2. **填写密码信息**
   - 在弹出的对话框中输入当前密码
   - 输入新密码（至少6位字符）
   - 再次输入新密码进行确认

3. **提交修改**
   - 点击"确认修改"按钮
   - 系统会验证当前密码是否正确
   - 验证通过后，新密码将生效

### 快捷键支持：
- `Enter`: 提交密码修改
- `Escape`: 取消修改并关闭对话框

## 安全特性

### 密码验证
- 必须提供正确的当前密码才能修改
- 新密码长度至少为6位字符
- 新密码不能与当前密码相同
- 新密码和确认密码必须一致

### 数据安全
- 所有密码都使用bcrypt进行加密存储
- 支持向后兼容旧的明文密码（自动升级为加密存储）
- 密码传输过程中使用HTTPS加密（生产环境）

### 用户体验
- 实时表单验证，及时提示错误信息
- 密码修改成功后自动清空表单
- 友好的错误提示信息

## 技术实现

### 后端API
- **路由**: `PUT /auth/change-password`
- **认证**: 需要有效的JWT token
- **参数**: 
  - `currentPassword`: 当前密码
  - `newPassword`: 新密码

### 前端组件
- 新增密码修改对话框组件
- 集成到主应用的状态管理中
- 响应式设计，支持移动端

### 数据库
- 使用现有的users表
- 密码字段支持bcrypt加密格式
- 兼容旧的明文密码格式

## 错误处理

### 常见错误及解决方法：

1. **"当前密码错误"**
   - 请确认输入的当前密码是否正确
   - 检查键盘大小写锁定状态

2. **"新密码长度至少为6位"**
   - 请输入至少6个字符的新密码
   - 建议使用字母、数字和特殊字符的组合

3. **"新密码和确认密码不匹配"**
   - 请确保两次输入的新密码完全一致
   - 注意检查空格和特殊字符

4. **"新密码不能与当前密码相同"**
   - 请选择一个与当前密码不同的新密码
   - 建议定期更换密码以提高安全性

## 测试功能

项目中包含了一个测试页面 `test-password-change.html`，可以用来测试密码修改API功能：

1. 启动后端服务器：`cd bankend && npm start`
2. 启动前端服务器：`npm run dev`
3. 访问测试页面：`http://localhost:5174/test-password-change.html`

## 注意事项

1. **密码安全**：请选择强密码，包含大小写字母、数字和特殊字符
2. **定期更换**：建议定期更换密码以提高账户安全性
3. **保密性**：请勿与他人分享您的密码
4. **备份**：修改密码后请确保记住新密码，避免忘记导致无法登录

## 后续计划

- [ ] 添加密码强度指示器
- [ ] 支持密码找回功能
- [ ] 添加登录历史记录
- [ ] 支持两步验证
- [ ] 密码过期提醒功能
