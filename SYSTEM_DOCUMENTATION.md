# 笔记应用系统文档

## 1. 系统架构

本系统采用前后端分离的架构：
- **前端**：基于 React + TypeScript + Vite 构建的单页应用(SPA)
- **后端**：Node.js + Express 框架构建的 RESTful API 服务
- **数据库**：MySQL
- **AI 集成**：集成了 Google GenAI 服务

## 2. 项目结构

### 2.1 前端结构 (`/`)
- `index.html` - 应用入口 HTML
- `index.tsx` - React 主组件
- `index.css` - 全局样式
- `vite.config.ts` - Vite 构建配置
- `tsconfig.json` - TypeScript 配置

### 2.2 后端结构 (`/bankend`)

#### 核心文件
- `server.js` - 应用入口文件，配置中间件和路由
- `db.js` - 数据库连接和初始化
- `.env` - 环境变量配置

#### 目录结构
- `/routes` - API 路由处理
  - `auth.js` - 用户认证相关接口
  - `folders.js` - 文件夹管理接口
  - `notes.js` - 笔记管理接口
  - `ai.js` - AI 相关接口
  - `knowledge.js` - 知识库管理接口

- `/services` - 业务逻辑层
  - `aiService.js` - AI 服务封装
  - `analysisService.js` - 内容分析服务
  - `embeddingService.js` - 文本嵌入服务
  - `organizationService.js` - 组织管理服务
  - `searchService.js` - 搜索服务

- `/middleware` - Express 中间件
  - 认证、日志、安全等中间件

- `/database` - 数据库相关
  - 迁移脚本和初始化SQL

- `/scripts` - 实用脚本
  - 数据修复和迁移工具

## 3. 主要功能模块

### 3.1 用户认证
- 基于 JWT 的认证机制
- 密码加密存储 (bcrypt)
- 登录/注册/登出功能
- 密码重置

### 3.2 笔记管理
- 创建/编辑/删除笔记
- 富文本编辑支持
- 笔记分类和标签
- 版本控制

### 3.3 知识库
- 知识库管理
- 文档上传和解析
- 智能搜索
- 内容分析

### 3.4 AI 功能
- 智能补全
- 内容摘要
- 文本分析
- 问答系统

## 4. API 接口

### 认证相关 (`/api/auth`)
- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `POST /refresh-token` - 刷新访问令牌
- `POST /logout` - 用户登出

### 笔记相关 (`/api/notes`)
- `GET /` - 获取笔记列表
- `POST /` - 创建新笔记
- `GET /:id` - 获取笔记详情
- `PUT /:id` - 更新笔记
- `DELETE /:id` - 删除笔记

### 知识库相关 (`/api/knowledge`)
- `POST /upload` - 上传文档到知识库
- `GET /documents` - 获取文档列表
- `POST /search` - 知识库搜索
- `DELETE /:id` - 删除文档

## 5. 安全措施

- CORS 配置白名单
- 请求频率限制
- 安全头部设置
- SQL 注入防护
- XSS 防护
- CSRF 防护
- JWT 令牌验证
- 密码加密存储

## 6. 开发环境配置

### 前端依赖
- React 18
- TypeScript
- Vite 6
- Google GenAI SDK

### 后端依赖
- Express 4.18
- MySQL2
- bcrypt (密码加密)
- jsonwebtoken (JWT)
- cors (跨域支持)
- dotenv (环境变量)
- express-rate-limit (请求限流)

## 7. 部署说明

### 环境变量
需要配置以下环境变量：
```
# 数据库配置
DB_HOST=localhost
DB_USER=user
DB_PASSWORD=password
DB_NAME=notes_db

# JWT 配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_refresh_secret
JWT_REFRESH_EXPIRES_IN=7d

# CORS 配置
CORS_ORIGIN=http://localhost:5173

# AI 服务配置
GEMINI_API_KEY=your_gemini_api_key
```

### 启动命令

1. 安装依赖
```bash
# 前端
cd /path/to/project
npm install

# 后端
cd /path/to/project/bankend
npm install
```

2. 启动开发服务器
```bash
# 前端
npm run dev

# 后端
npm run dev
```

3. 构建生产版本
```bash
# 前端
npm run build

# 后端
npm start
```

## 8. 常见问题

### 8.1 数据库连接失败
- 检查数据库服务是否运行
- 验证数据库配置信息
- 检查数据库用户权限

### 8.2 跨域问题
- 确保前端域名在 CORS 白名单中
- 检查请求头设置

### 8.3 AI 功能不可用
- 检查 API 密钥配置
- 验证网络连接
- 检查服务配额

## 9. 后续开发计划

- 实现实时协作编辑
- 添加更多文件格式支持
- 增强搜索功能
- 优化移动端体验
- 增加数据分析看板

## 10. 贡献指南

1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request
