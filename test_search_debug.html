<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索调试测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔍 搜索功能调试测试</h1>
    
    <div class="test-section">
        <h3>🔐 登录测试</h3>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="123456">
        <button class="test-button" onclick="testLogin()">登录</button>
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>🔍 搜索测试</h3>
        <input type="text" id="searchQuery" placeholder="输入搜索内容" value="JVM" style="width: 200px;">
        <select id="searchType">
            <option value="semantic">语义搜索</option>
            <option value="keyword">关键词搜索</option>
            <option value="hybrid">混合搜索</option>
        </select>
        <button class="test-button" onclick="testSearch()">测试搜索</button>
        <div id="searchResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        let authToken = '';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
                        ...options.headers
                    }
                });

                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${result.success ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(result, null, 2);
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                alert('请输入用户名和密码');
                return;
            }

            const result = await makeRequest(`${API_BASE}/auth/login`, {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });

            if (result.success && result.data.token) {
                authToken = result.data.token;
                console.log('Login successful, token:', authToken);
            }

            showResult('loginResult', result);
        }

        async function testSearch() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const query = document.getElementById('searchQuery').value;
            const searchType = document.getElementById('searchType').value;
            
            if (!query.trim()) {
                alert('请输入搜索内容');
                return;
            }

            console.log('Testing search:', { query, searchType });

            const result = await makeRequest(`${API_BASE}/knowledge/search`, {
                method: 'POST',
                body: JSON.stringify({
                    query,
                    searchType,
                    options: {
                        limit: 10,
                        threshold: 0.7,
                        includeContent: false
                    }
                })
            });

            console.log('Search result:', result);
            showResult('searchResult', result);
        }

        // 页面加载时自动尝试登录
        window.onload = function() {
            console.log('Page loaded, testing connection...');
        };
    </script>
</body>
</html>
