const mysql = require('mysql2/promise');
require('dotenv').config();

// 验证数据库配置
if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASSWORD || !process.env.DB_DATABASE) {
  console.error('SECURITY ERROR: Database configuration incomplete');
  process.exit(1);
}

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  // 安全配置
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false // 在生产环境中应该设置为true
  } : false,
  charset: 'utf8mb4',
  timezone: '+00:00',
  // 启用多语句查询保护
  multipleStatements: false
});

// 数据库查询日志包装器
const executeWithLog = async (query, params = []) => {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  console.log(`\n[${timestamp}] [DB] ========== 数据库查询 ==========`);
  console.log(`[DB] SQL: ${query}`);
  console.log(`[DB] 参数:`, params);
  
  try {
    const result = await pool.execute(query, params);
    const duration = Date.now() - startTime;
    
    console.log(`[DB] 查询成功 - 耗时: ${duration}ms`);
    console.log(`[DB] 影响行数: ${result[0]?.length || result[1]?.affectedRows || 0}`);
    
    // 如果是SELECT查询，显示结果概要
    if (query.trim().toUpperCase().startsWith('SELECT')) {
      console.log(`[DB] 返回记录数: ${result[0]?.length || 0}`);
      if (result[0]?.length > 0) {
        console.log(`[DB] 第一条记录字段:`, Object.keys(result[0][0]));
      }
    }
    
    // 如果是INSERT查询，显示插入ID
    if (query.trim().toUpperCase().startsWith('INSERT')) {
      console.log(`[DB] 插入ID: ${result[1]?.insertId || 'N/A'}`);
    }
    
    console.log(`[${new Date().toISOString()}] [DB] ========== 查询结束 ==========\n`);
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[DB] 查询失败 - 耗时: ${duration}ms`);
    console.error(`[DB] 错误信息:`, error.message);
    console.error(`[DB] 错误代码:`, error.code);
    console.log(`[${new Date().toISOString()}] [DB] ========== 查询错误结束 ==========\n`);
    throw error;
  }
};

// 测试连接
async function testConnection() {
  try {
    console.log(`[DB] 正在测试数据库连接...`);
    console.log(`[DB] 连接配置: ${process.env.DB_HOST}:${process.env.DB_PORT || 3306}/${process.env.DB_DATABASE}`);
    
    const connection = await pool.getConnection();
    console.log(`[DB] 数据库连接成功`);
    
    connection.release();
  } catch (error) {
    console.error(`[DB] 数据库连接失败:`, error.message);
    console.error(`[DB] 错误详情:`, error);
    process.exit(1);
  }
}

module.exports = {
  pool,
  executeWithLog,
  testConnection
}; 