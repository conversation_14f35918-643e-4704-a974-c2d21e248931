const { executeWithLog } = require('./db');
require('dotenv').config();

async function fixAnalysisData() {
    try {
        console.log('开始修复分析数据...');
        
        // 获取所有分析记录
        const [analyses] = await executeWithLog(
            'SELECT id, note_id, keywords, topics FROM note_analysis',
            []
        );
        
        console.log(`找到 ${analyses.length} 条分析记录`);
        
        let fixedCount = 0;
        
        for (const analysis of analyses) {
            let needsUpdate = false;
            let newKeywords = analysis.keywords;
            let newTopics = analysis.topics;
            
            // 修复关键词
            try {
                if (analysis.keywords) {
                    // 尝试解析JSON
                    JSON.parse(analysis.keywords);
                    console.log(`记录 ${analysis.id}: 关键词JSON格式正确`);
                } else {
                    newKeywords = '[]';
                    needsUpdate = true;
                }
            } catch (error) {
                console.log(`记录 ${analysis.id}: 修复关键词格式 - ${analysis.keywords}`);
                
                // 如果不是有效JSON，尝试修复
                if (typeof analysis.keywords === 'string') {
                    // 移除可能的JSON格式字符
                    const cleaned = analysis.keywords.replace(/[\[\]"]/g, '');

                    // 按逗号分割
                    const items = cleaned.split(',')
                        .map(item => item.trim())
                        .filter(item => item.length > 0);

                    newKeywords = JSON.stringify(items.length > 0 ? items : []);
                    needsUpdate = true;
                } else {
                    newKeywords = '[]';
                    needsUpdate = true;
                }
            }
            
            // 修复主题
            try {
                if (analysis.topics) {
                    // 尝试解析JSON
                    JSON.parse(analysis.topics);
                    console.log(`记录 ${analysis.id}: 主题JSON格式正确`);
                } else {
                    newTopics = '["未分类"]';
                    needsUpdate = true;
                }
            } catch (error) {
                console.log(`记录 ${analysis.id}: 修复主题格式 - ${analysis.topics}`);
                
                // 如果不是有效JSON，尝试修复
                if (typeof analysis.topics === 'string') {
                    // 移除可能的JSON格式字符
                    const cleaned = analysis.topics.replace(/[\[\]"]/g, '');

                    // 按逗号分割
                    const items = cleaned.split(',')
                        .map(item => item.trim())
                        .filter(item => item.length > 0);

                    newTopics = JSON.stringify(items.length > 0 ? items : ['未分类']);
                    needsUpdate = true;
                } else {
                    newTopics = '["未分类"]';
                    needsUpdate = true;
                }
            }
            
            // 如果需要更新，执行更新
            if (needsUpdate) {
                try {
                    await executeWithLog(
                        'UPDATE note_analysis SET keywords = ?, topics = ? WHERE id = ?',
                        [newKeywords, newTopics, analysis.id]
                    );
                    
                    console.log(`✅ 记录 ${analysis.id} (笔记 ${analysis.note_id}) 已修复`);
                    console.log(`   关键词: ${newKeywords}`);
                    console.log(`   主题: ${newTopics}`);
                    fixedCount++;
                } catch (updateError) {
                    console.error(`❌ 更新记录 ${analysis.id} 失败:`, updateError);
                }
            }
        }
        
        console.log(`\n修复完成！共修复了 ${fixedCount} 条记录`);
        
        // 验证修复结果
        console.log('\n验证修复结果...');
        const [verifyAnalyses] = await executeWithLog(
            'SELECT id, note_id, keywords, topics FROM note_analysis LIMIT 5',
            []
        );
        
        for (const analysis of verifyAnalyses) {
            try {
                const keywords = JSON.parse(analysis.keywords || '[]');
                const topics = JSON.parse(analysis.topics || '[]');
                console.log(`✅ 记录 ${analysis.id}: 关键词 ${keywords.length} 个, 主题 ${topics.length} 个`);
            } catch (error) {
                console.log(`❌ 记录 ${analysis.id}: 仍有JSON格式问题`);
            }
        }
        
    } catch (error) {
        console.error('修复过程中出错:', error);
    }
}

// 运行修复脚本
fixAnalysisData();
