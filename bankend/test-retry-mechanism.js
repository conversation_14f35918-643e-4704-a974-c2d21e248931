const aiService = require('./services/aiService');
require('dotenv').config();

async function testRetryMechanism() {
    try {
        console.log('Testing AI Service Retry Mechanism...');
        
        if (!aiService.isAvailable()) {
            console.log('AI Service not available, exiting test');
            return;
        }
        
        console.log('AI Service available:', aiService.isAvailable());
        console.log('Model info:', aiService.getModelInfo());
        
        // 测试1: 正常的内容生成（可能会遇到503错误并重试）
        console.log('\n--- Test 1: Content Generation with Retry ---');
        try {
            const startTime = Date.now();
            const result = await aiService.generateContent('请用中文简单介绍一下人工智能。');
            const endTime = Date.now();
            
            console.log('✅ Content generation successful');
            console.log('Response time:', endTime - startTime, 'ms');
            console.log('Generated text preview:', result.response.text().substring(0, 100) + '...');
        } catch (error) {
            console.log('❌ Content generation failed:', error.message);
        }
        
        // 测试2: Embedding生成（可能会遇到503错误并重试）
        console.log('\n--- Test 2: Embedding Generation with Retry ---');
        try {
            const startTime = Date.now();
            const embedding = await aiService.generateEmbedding('这是一个测试文本用于生成向量嵌入。');
            const endTime = Date.now();
            
            console.log('✅ Embedding generation successful');
            console.log('Response time:', endTime - startTime, 'ms');
            console.log('Embedding length:', embedding.length);
            console.log('First 5 values:', embedding.slice(0, 5));
        } catch (error) {
            console.log('❌ Embedding generation failed:', error.message);
        }
        
        // 测试3: 内容分析（可能会遇到503错误并重试）
        console.log('\n--- Test 3: Content Analysis with Retry ---');
        try {
            const startTime = Date.now();
            const analysis = await aiService.analyzeContent('分析这段文本：人工智能正在改变世界，它带来了机遇也带来了挑战。');
            const endTime = Date.now();
            
            console.log('✅ Content analysis successful');
            console.log('Response time:', endTime - startTime, 'ms');
            console.log('Analysis preview:', analysis.substring(0, 100) + '...');
        } catch (error) {
            console.log('❌ Content analysis failed:', error.message);
        }
        
        // 测试4: 多次快速请求（测试限流和重试）
        console.log('\n--- Test 4: Multiple Rapid Requests ---');
        const promises = [];
        for (let i = 0; i < 3; i++) {
            promises.push(
                aiService.generateContent(`这是第${i+1}个测试请求，请简短回复。`)
                    .then(result => {
                        console.log(`✅ Request ${i+1} successful: ${result.response.text().substring(0, 50)}...`);
                        return { success: true, index: i+1 };
                    })
                    .catch(error => {
                        console.log(`❌ Request ${i+1} failed: ${error.message}`);
                        return { success: false, index: i+1, error: error.message };
                    })
            );
        }
        
        const results = await Promise.all(promises);
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        console.log(`\nBatch results: ${successful} successful, ${failed} failed`);
        
        console.log('\n--- Retry Mechanism Test Completed ---');
        
    } catch (error) {
        console.error('Test error:', error);
    }
}

// 运行测试
testRetryMechanism();
