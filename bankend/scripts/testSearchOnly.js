#!/usr/bin/env node

/**
 * 测试搜索功能（不触发批量处理）
 */

const SearchService = require('../services/searchService');

async function testSearchFunctions() {
    console.log('🔍 测试搜索功能（不触发批量处理）\n');
    
    const searchService = new SearchService();
    const userId = 3;
    const query = 'JVM';

    console.log(`搜索查询: "${query}"`);
    console.log('=' .repeat(50));

    // 1. 测试关键词搜索
    try {
        console.log('\n🔤 关键词搜索:');
        const startTime = Date.now();
        
        const keywordResults = await searchService.keywordSearch(userId, query, {
            limit: 5,
            includeContent: false
        });
        
        const keywordTime = Date.now() - startTime;
        console.log(`  ✅ 成功 - 找到 ${keywordResults.results.length} 个结果，耗时 ${keywordTime}ms`);
        
        keywordResults.results.forEach((result, index) => {
            console.log(`    ${index + 1}. ${result.title} (笔记ID: ${result.noteId})`);
        });
        
    } catch (error) {
        console.log(`  ❌ 失败: ${error.message}`);
    }

    // 2. 测试语义搜索
    try {
        console.log('\n🧠 语义搜索:');
        const startTime = Date.now();
        
        const semanticResults = await searchService.semanticSearch(userId, query, {
            limit: 5,
            threshold: 0.6,
            includeContent: false
        });
        
        const semanticTime = Date.now() - startTime;
        console.log(`  ✅ 成功 - 找到 ${semanticResults.results.length} 个结果，耗时 ${semanticTime}ms`);
        
        semanticResults.results.forEach((result, index) => {
            console.log(`    ${index + 1}. ${result.title} (相似度: ${result.similarity.toFixed(3)})`);
            console.log(`       匹配文本: ${result.matchedText.substring(0, 60)}...`);
        });
        
    } catch (error) {
        console.log(`  ❌ 失败: ${error.message}`);
        if (error.message.includes('embedding')) {
            console.log('    💡 提示: 语义搜索需要网络连接到Google Gemini API');
        }
    }

    // 3. 测试混合搜索
    try {
        console.log('\n🔀 混合搜索:');
        const startTime = Date.now();
        
        const hybridResults = await searchService.hybridSearch(userId, query, {
            limit: 5,
            threshold: 0.6,
            includeContent: false
        });
        
        const hybridTime = Date.now() - startTime;
        console.log(`  ✅ 成功 - 找到 ${hybridResults.results.length} 个结果，耗时 ${hybridTime}ms`);
        
        hybridResults.results.forEach((result, index) => {
            console.log(`    ${index + 1}. ${result.title} (分数: ${result.score?.toFixed(3) || 'N/A'})`);
        });
        
    } catch (error) {
        console.log(`  ❌ 失败: ${error.message}`);
    }

    console.log('\n📊 测试总结:');
    console.log('- 关键词搜索: 基于文本匹配，速度快，适合精确查找');
    console.log('- 语义搜索: 基于AI理解，能找到语义相关内容');
    console.log('- 混合搜索: 结合两种方式，提供更全面的结果');
    console.log('\n💡 如果语义搜索失败，系统会自动降级到关键词搜索');
}

// 运行测试
if (require.main === module) {
    testSearchFunctions()
        .then(() => {
            console.log('\n✅ 搜索功能测试完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ 测试失败:', error);
            process.exit(1);
        });
}

module.exports = testSearchFunctions;
