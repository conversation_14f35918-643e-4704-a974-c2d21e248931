const { executeWithLog } = require('../db');

/**
 * 直接创建知识库相关表
 */
async function createKnowledgeTables() {
    try {
        console.log('开始创建知识库数据表...');

        // 1. 创建笔记向量存储表
        console.log('创建 note_vectors 表...');
        await executeWithLog(`
            CREATE TABLE IF NOT EXISTS note_vectors (
                id INT PRIMARY KEY AUTO_INCREMENT,
                note_id INT NOT NULL,
                user_id INT NOT NULL,
                content_hash VARCHAR(64) NOT NULL COMMENT '内容哈希，用于检测内容变化',
                embedding_vector JSON NOT NULL COMMENT '内容向量',
                chunk_index INT DEFAULT 0 COMMENT '分块索引，长文本分块存储',
                chunk_text TEXT NOT NULL COMMENT '分块文本内容',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIG<PERSON> KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                
                INDEX idx_note_vectors_note_id (note_id),
                INDEX idx_note_vectors_user_id (user_id),
                INDEX idx_note_vectors_hash (content_hash)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 2. 创建笔记分析结果表
        console.log('创建 note_analysis 表...');
        await executeWithLog(`
            CREATE TABLE IF NOT EXISTS note_analysis (
                id INT PRIMARY KEY AUTO_INCREMENT,
                note_id INT NOT NULL,
                user_id INT NOT NULL,
                
                summary TEXT COMMENT 'AI生成的摘要',
                keywords JSON COMMENT '关键词列表',
                topics JSON COMMENT '主题标签',
                sentiment_score DECIMAL(3,2) COMMENT '情感分数 (-1到1)',
                complexity_score DECIMAL(3,2) COMMENT '复杂度分数 (0到1)',
                
                word_count INT DEFAULT 0,
                char_count INT DEFAULT 0,
                language VARCHAR(10) DEFAULT 'zh-CN',
                
                category VARCHAR(100) COMMENT 'AI推荐分类',
                confidence_score DECIMAL(3,2) COMMENT '分类置信度',
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_note_analysis (note_id),
                INDEX idx_analysis_user_id (user_id),
                INDEX idx_analysis_category (category)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 3. 创建笔记关联关系表
        console.log('创建 note_relations 表...');
        await executeWithLog(`
            CREATE TABLE IF NOT EXISTS note_relations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                source_note_id INT NOT NULL,
                target_note_id INT NOT NULL,
                
                relation_type ENUM('similar', 'reference', 'follow_up', 'contradiction', 'supplement') NOT NULL,
                similarity_score DECIMAL(4,3) NOT NULL COMMENT '相似度分数 (0到1)',
                
                relation_reason TEXT COMMENT 'AI分析的关联原因',
                
                status ENUM('auto', 'confirmed', 'rejected') DEFAULT 'auto',
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (source_note_id) REFERENCES notes(id) ON DELETE CASCADE,
                FOREIGN KEY (target_note_id) REFERENCES notes(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_relation (source_note_id, target_note_id),
                INDEX idx_relations_user_id (user_id),
                INDEX idx_relations_source (source_note_id),
                INDEX idx_relations_target (target_note_id),
                INDEX idx_relations_type (relation_type),
                INDEX idx_relations_similarity (similarity_score)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 4. 创建知识库搜索历史表
        console.log('创建 search_history 表...');
        await executeWithLog(`
            CREATE TABLE IF NOT EXISTS search_history (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                query_text TEXT NOT NULL,
                query_vector JSON COMMENT '查询向量',
                
                result_count INT DEFAULT 0,
                result_note_ids JSON COMMENT '搜索到的笔记ID列表',
                
                search_type ENUM('semantic', 'keyword', 'hybrid') DEFAULT 'semantic',
                similarity_threshold DECIMAL(3,2) DEFAULT 0.7,
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                
                INDEX idx_search_user_id (user_id),
                INDEX idx_search_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 5. 创建智能标签表
        console.log('创建 smart_tags 表...');
        await executeWithLog(`
            CREATE TABLE IF NOT EXISTS smart_tags (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                tag_name VARCHAR(100) NOT NULL,
                tag_type ENUM('auto', 'manual', 'system') DEFAULT 'auto',
                
                usage_count INT DEFAULT 0,
                
                tag_vector JSON COMMENT '标签向量',
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_user_tag (user_id, tag_name),
                INDEX idx_tags_type (tag_type),
                INDEX idx_tags_usage (usage_count)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 6. 创建笔记标签关联表
        console.log('创建 note_tags 表...');
        await executeWithLog(`
            CREATE TABLE IF NOT EXISTS note_tags (
                id INT PRIMARY KEY AUTO_INCREMENT,
                note_id INT NOT NULL,
                tag_id INT NOT NULL,
                confidence_score DECIMAL(3,2) DEFAULT 1.0 COMMENT '标签置信度',
                assigned_by ENUM('ai', 'user') DEFAULT 'ai',
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
                FOREIGN KEY (tag_id) REFERENCES smart_tags(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_note_tag (note_id, tag_id),
                INDEX idx_note_tags_confidence (confidence_score)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // 7. 创建知识库配置表
        console.log('创建 knowledge_base_config 表...');
        await executeWithLog(`
            CREATE TABLE IF NOT EXISTS knowledge_base_config (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                
                embedding_model VARCHAR(100) DEFAULT 'text-embedding-004',
                chunk_size INT DEFAULT 1000,
                chunk_overlap INT DEFAULT 200,
                
                default_similarity_threshold DECIMAL(3,2) DEFAULT 0.7,
                max_search_results INT DEFAULT 20,
                
                auto_analysis_enabled BOOLEAN DEFAULT TRUE,
                auto_tagging_enabled BOOLEAN DEFAULT TRUE,
                auto_relation_enabled BOOLEAN DEFAULT TRUE,
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_user_config (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        console.log('✅ 所有知识库数据表创建完成！');

        // 验证表是否创建成功
        await verifyTables();

    } catch (error) {
        console.error('❌ 创建知识库数据表失败:', error);
        throw error;
    }
}

/**
 * 验证表是否创建成功
 */
async function verifyTables() {
    try {
        console.log('\n验证数据库表...');

        const expectedTables = [
            'note_vectors',
            'note_analysis',
            'note_relations',
            'search_history',
            'smart_tags',
            'note_tags',
            'knowledge_base_config'
        ];

        for (const tableName of expectedTables) {
            try {
                const [result] = await executeWithLog(
                    `SELECT COUNT(*) as count FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = ?`,
                    [tableName]
                );

                if (result[0].count > 0) {
                    console.log(`✓ 表 ${tableName} 存在`);
                } else {
                    console.log(`✗ 表 ${tableName} 不存在`);
                }
            } catch (error) {
                console.log(`✗ 检查表 ${tableName} 时出错:`, error.message);
            }
        }

        console.log('✅ 表验证完成');

    } catch (error) {
        console.error('验证表时出错:', error);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    createKnowledgeTables()
        .then(() => {
            console.log('数据库初始化完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('数据库初始化失败:', error);
            process.exit(1);
        });
}

module.exports = {
    createKnowledgeTables,
    verifyTables
};
