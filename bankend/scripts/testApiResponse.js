#!/usr/bin/env node

/**
 * 测试知识库搜索API的返回格式
 */

const express = require('express');
const SearchService = require('../services/searchService');

async function testApiResponse() {
    console.log('🔍 测试知识库搜索API返回格式\n');
    
    const searchService = new SearchService();
    const userId = 3;
    const query = 'JVM';

    try {
        // 1. 测试语义搜索
        console.log('1. 测试语义搜索API格式:');
        const semanticResult = await searchService.semanticSearch(userId, query, {
            limit: 5,
            threshold: 0.6,
            includeContent: false
        });
        
        console.log('语义搜索原始返回:');
        console.log(JSON.stringify(semanticResult, null, 2));
        
        // 模拟API路由的返回格式
        const apiResponse = {
            success: true,
            data: semanticResult
        };
        
        console.log('\nAPI路由返回格式:');
        console.log(JSON.stringify(apiResponse, null, 2));
        
        console.log('\n前端应该访问的路径:');
        console.log('- result.data.results:', apiResponse.data.results?.length || 0, '个结果');
        console.log('- result.data.totalCount:', apiResponse.data.totalCount);
        console.log('- result.data.searchTime:', apiResponse.data.searchTime + 'ms');
        
        // 2. 测试关键词搜索
        console.log('\n' + '='.repeat(50));
        console.log('2. 测试关键词搜索API格式:');
        
        const keywordResult = await searchService.keywordSearch(userId, query, {
            limit: 5,
            includeContent: false
        });
        
        console.log('关键词搜索原始返回:');
        console.log(JSON.stringify(keywordResult, null, 2));
        
        const keywordApiResponse = {
            success: true,
            data: keywordResult
        };
        
        console.log('\nAPI路由返回格式:');
        console.log(JSON.stringify(keywordApiResponse, null, 2));
        
        // 3. 验证数据结构
        console.log('\n' + '='.repeat(50));
        console.log('3. 数据结构验证:');
        
        const testResult = apiResponse.data?.results || [];
        console.log('✅ 前端解析测试:');
        console.log(`   result.data?.results: ${testResult.length} 个结果`);
        
        if (testResult.length > 0) {
            console.log('   第一个结果结构:');
            const firstResult = testResult[0];
            console.log(`   - noteId: ${firstResult.noteId}`);
            console.log(`   - title: ${firstResult.title}`);
            console.log(`   - similarity: ${firstResult.similarity || 'N/A'}`);
            console.log(`   - matchedText: ${firstResult.matchedText?.substring(0, 50) || 'N/A'}...`);
        }
        
        console.log('\n✅ API格式验证完成');
        console.log('📋 总结:');
        console.log('- 后端返回: { success: true, data: { results: [...], totalCount: N, searchTime: N } }');
        console.log('- 前端应访问: result.data.results');
        console.log('- 数据结构正确，前端应该能正常显示结果');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

// 运行测试
if (require.main === module) {
    testApiResponse()
        .then(() => {
            console.log('\n✅ API格式测试完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ 测试失败:', error);
            process.exit(1);
        });
}

module.exports = testApiResponse;
