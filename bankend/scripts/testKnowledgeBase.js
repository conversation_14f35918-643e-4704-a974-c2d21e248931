const EmbeddingService = require('../services/embeddingService');
const SearchService = require('../services/searchService');
const AnalysisService = require('../services/analysisService');
const OrganizationService = require('../services/organizationService');

/**
 * 测试知识库功能
 */
async function testKnowledgeBase() {
    try {
        console.log('🧪 开始测试知识库功能...\n');

        // 测试用户ID（请根据实际情况修改）
        const userId = 3;
        const testNoteId = 40; // 请根据实际情况修改

        // 1. 测试向量化服务
        console.log('📊 测试向量化服务...');
        const embeddingService = new EmbeddingService();
        
        try {
            // 测试单个笔记向量化
            const vectorResult = await embeddingService.processNoteContent(
                testNoteId, 
                userId, 
                '这是一个测试笔记，包含一些示例内容用于测试向量化功能。'
            );
            console.log('✅ 向量化测试成功:', vectorResult.status);
        } catch (error) {
            console.log('❌ 向量化测试失败:', error.message);
        }

        // 2. 测试内容分析服务
        console.log('\n📈 测试内容分析服务...');
        const analysisService = new AnalysisService();
        
        try {
            const analysisResult = await analysisService.analyzeNoteContent(
                testNoteId,
                userId,
                '测试笔记标题',
                '这是一个关于项目管理的笔记。项目管理是一个复杂的过程，需要协调各种资源和时间安排。良好的项目管理可以提高效率，确保项目按时完成。'
            );
            console.log('✅ 内容分析测试成功');
            console.log('   - 摘要:', analysisResult.summary.substring(0, 50) + '...');
            console.log('   - 关键词:', analysisResult.keywords.slice(0, 3));
            console.log('   - 分类:', analysisResult.category);
        } catch (error) {
            console.log('❌ 内容分析测试失败:', error.message);
        }

        // 3. 测试搜索服务
        console.log('\n🔍 测试搜索服务...');
        const searchService = new SearchService();
        
        try {
            // 测试关键词搜索
            const keywordResults = await searchService.keywordSearch(userId, '项目', {
                limit: 5
            });
            console.log('✅ 关键词搜索测试成功');
            console.log(`   - 找到 ${keywordResults.results.length} 个结果`);

            // 如果有向量数据，测试语义搜索
            try {
                const semanticResults = await searchService.semanticSearch(userId, '项目管理相关内容', {
                    limit: 5,
                    threshold: 0.5
                });
                console.log('✅ 语义搜索测试成功');
                console.log(`   - 找到 ${semanticResults.results.length} 个结果`);
            } catch (error) {
                console.log('⚠️  语义搜索需要先进行向量化处理');
            }
        } catch (error) {
            console.log('❌ 搜索测试失败:', error.message);
        }

        // 4. 测试组织服务
        console.log('\n🔗 测试组织服务...');
        const organizationService = new OrganizationService();
        
        try {
            // 测试文件夹推荐
            const folderRecommendations = await organizationService.recommendFolderStructure(userId);
            console.log('✅ 文件夹推荐测试成功');
            console.log(`   - 生成 ${folderRecommendations.recommendations.length} 个推荐`);
            
            if (folderRecommendations.recommendations.length > 0) {
                console.log('   - 示例推荐:', folderRecommendations.recommendations[0].name);
            }
        } catch (error) {
            console.log('❌ 组织服务测试失败:', error.message);
        }

        console.log('\n🎉 知识库功能测试完成！');
        console.log('\n📝 使用建议:');
        console.log('1. 如果语义搜索失败，请先运行批量向量化处理');
        console.log('2. 确保GEMINI_API_KEY已正确配置');
        console.log('3. 网络连接正常，可以访问Google AI服务');

    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

/**
 * 批量处理用户笔记（用于初始化）
 */
async function initializeUserKnowledge(userId) {
    try {
        console.log(`🚀 开始为用户 ${userId} 初始化知识库...\n`);

        const embeddingService = new EmbeddingService();
        const analysisService = new AnalysisService();
        const organizationService = new OrganizationService();

        // 1. 批量向量化
        console.log('📊 批量向量化处理...');
        const vectorResults = await embeddingService.processAllUserNotes(userId);
        console.log(`✅ 向量化完成: 处理 ${vectorResults.processed} 个笔记`);

        // 2. 批量分析
        console.log('\n📈 批量内容分析...');
        const analysisResults = await analysisService.analyzeAllUserNotes(userId);
        console.log(`✅ 分析完成: 分析 ${analysisResults.analyzed} 个笔记`);

        // 3. 发现关联关系
        console.log('\n🔗 发现笔记关联...');
        const relationResults = await organizationService.discoverNoteRelations(userId);
        console.log(`✅ 关联发现完成: 发现 ${relationResults.relationsFound} 个关联`);

        // 4. 自动标签管理
        console.log('\n🏷️  自动标签管理...');
        const tagResults = await organizationService.autoManageTags(userId);
        console.log(`✅ 标签管理完成: 创建 ${tagResults.tagsCreated} 个标签`);

        console.log('\n🎉 知识库初始化完成！');
        console.log('现在可以使用语义搜索和其他高级功能了。');

    } catch (error) {
        console.error('❌ 初始化过程中出现错误:', error);
    }
}

// 命令行参数处理
const command = process.argv[2];
const userId = parseInt(process.argv[3]) || 3;

switch (command) {
    case 'test':
        testKnowledgeBase();
        break;
    case 'init':
        initializeUserKnowledge(userId);
        break;
    default:
        console.log(`
知识库测试和初始化工具

用法:
  node testKnowledgeBase.js test           # 测试知识库功能
  node testKnowledgeBase.js init [userId]  # 初始化用户知识库

示例:
  node testKnowledgeBase.js test
  node testKnowledgeBase.js init 3
        `);
        break;
}
