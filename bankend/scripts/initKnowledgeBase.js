const fs = require('fs');
const path = require('path');
const { executeWithLog } = require('../db');

/**
 * 初始化知识库数据库结构
 */
async function initKnowledgeBase() {
    try {
        console.log('开始初始化知识库数据库结构...');

        // 读取SQL文件
        const sqlFile = path.join(__dirname, '../database/knowledge_base_schema.sql');
        const sqlContent = fs.readFileSync(sqlFile, 'utf8');

        // 分割SQL语句
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

        console.log(`找到 ${statements.length} 个SQL语句`);

        // 执行每个SQL语句
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            
            try {
                console.log(`执行语句 ${i + 1}/${statements.length}...`);
                await executeWithLog(statement);
                console.log(`✓ 语句 ${i + 1} 执行成功`);
            } catch (error) {
                // 如果是表已存在的错误，跳过
                if (error.code === 'ER_TABLE_EXISTS_ERROR') {
                    console.log(`⚠ 语句 ${i + 1} 跳过（表已存在）`);
                    continue;
                }
                
                console.error(`✗ 语句 ${i + 1} 执行失败:`, error.message);
                console.log('失败的SQL语句:', statement.substring(0, 100) + '...');
                
                // 继续执行其他语句
                continue;
            }
        }

        console.log('✅ 知识库数据库结构初始化完成！');

        // 验证表是否创建成功
        await verifyTables();

    } catch (error) {
        console.error('❌ 初始化知识库数据库失败:', error);
        throw error;
    }
}

/**
 * 验证表是否创建成功
 */
async function verifyTables() {
    try {
        console.log('\n验证数据库表...');

        const expectedTables = [
            'note_vectors',
            'note_analysis',
            'note_relations',
            'search_history',
            'smart_tags',
            'note_tags',
            'knowledge_base_config'
        ];

        for (const tableName of expectedTables) {
            try {
                const [result] = await executeWithLog(
                    `SELECT COUNT(*) as count FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = ?`,
                    [tableName]
                );

                if (result[0].count > 0) {
                    console.log(`✓ 表 ${tableName} 存在`);
                } else {
                    console.log(`✗ 表 ${tableName} 不存在`);
                }
            } catch (error) {
                console.log(`✗ 检查表 ${tableName} 时出错:`, error.message);
            }
        }

        console.log('✅ 表验证完成');

    } catch (error) {
        console.error('验证表时出错:', error);
    }
}

/**
 * 为用户初始化知识库配置
 */
async function initUserKnowledgeConfig(userId) {
    try {
        console.log(`为用户 ${userId} 初始化知识库配置...`);

        // 检查是否已有配置
        const [existing] = await executeWithLog(
            'SELECT id FROM knowledge_base_config WHERE user_id = ?',
            [userId]
        );

        if (existing.length > 0) {
            console.log(`用户 ${userId} 已有知识库配置，跳过初始化`);
            return;
        }

        // 创建默认配置
        await executeWithLog(
            `INSERT INTO knowledge_base_config 
             (user_id, embedding_model, chunk_size, chunk_overlap, 
              default_similarity_threshold, max_search_results,
              auto_analysis_enabled, auto_tagging_enabled, auto_relation_enabled)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                userId,
                'text-embedding-004',
                1000,
                200,
                0.7,
                20,
                true,
                true,
                true
            ]
        );

        console.log(`✅ 用户 ${userId} 知识库配置初始化完成`);

    } catch (error) {
        console.error(`初始化用户 ${userId} 知识库配置失败:`, error);
        throw error;
    }
}

/**
 * 清理知识库数据（危险操作，仅用于开发环境）
 */
async function cleanKnowledgeBase(userId = null) {
    try {
        if (process.env.NODE_ENV === 'production') {
            throw new Error('生产环境不允许清理知识库数据');
        }

        console.log('⚠️  开始清理知识库数据...');

        const tables = [
            'note_vectors',
            'note_analysis', 
            'note_relations',
            'search_history',
            'note_tags',
            'smart_tags',
            'knowledge_base_config'
        ];

        for (const table of tables) {
            try {
                if (userId) {
                    await executeWithLog(`DELETE FROM ${table} WHERE user_id = ?`, [userId]);
                    console.log(`✓ 清理表 ${table} 中用户 ${userId} 的数据`);
                } else {
                    await executeWithLog(`DELETE FROM ${table}`);
                    console.log(`✓ 清理表 ${table} 的所有数据`);
                }
            } catch (error) {
                console.log(`✗ 清理表 ${table} 时出错:`, error.message);
            }
        }

        console.log('✅ 知识库数据清理完成');

    } catch (error) {
        console.error('清理知识库数据失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const command = process.argv[2];
    const userId = process.argv[3];

    switch (command) {
        case 'init':
            initKnowledgeBase()
                .then(() => {
                    console.log('初始化完成');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('初始化失败:', error);
                    process.exit(1);
                });
            break;

        case 'init-user':
            if (!userId) {
                console.error('请提供用户ID: node initKnowledgeBase.js init-user <userId>');
                process.exit(1);
            }
            initUserKnowledgeConfig(parseInt(userId))
                .then(() => {
                    console.log('用户配置初始化完成');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('用户配置初始化失败:', error);
                    process.exit(1);
                });
            break;

        case 'clean':
            cleanKnowledgeBase(userId ? parseInt(userId) : null)
                .then(() => {
                    console.log('清理完成');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('清理失败:', error);
                    process.exit(1);
                });
            break;

        case 'verify':
            verifyTables()
                .then(() => {
                    console.log('验证完成');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('验证失败:', error);
                    process.exit(1);
                });
            break;

        default:
            console.log(`
知识库初始化脚本

用法:
  node initKnowledgeBase.js init              # 初始化数据库结构
  node initKnowledgeBase.js init-user <id>    # 为用户初始化配置
  node initKnowledgeBase.js verify            # 验证表结构
  node initKnowledgeBase.js clean [userId]    # 清理数据（开发环境）

示例:
  node initKnowledgeBase.js init
  node initKnowledgeBase.js init-user 1
  node initKnowledgeBase.js clean 1
            `);
            process.exit(0);
    }
}

module.exports = {
    initKnowledgeBase,
    initUserKnowledgeConfig,
    cleanKnowledgeBase,
    verifyTables
};
