#!/usr/bin/env node

/**
 * 语义搜索演示脚本
 * 展示语义搜索与关键词搜索的区别
 */

const SearchService = require('../services/searchService');
const { executeWithLog } = require('../db');

class SemanticSearchDemo {
    constructor() {
        this.searchService = new SearchService();
    }

    async demonstrateSearchDifference() {
        console.log('🔍 语义搜索 vs 关键词搜索演示\n');
        
        const userId = 3;
        const queries = [
            'JVM',
            'Java虚拟机',
            '内存管理',
            '垃圾回收',
            '性能优化'
        ];

        for (const query of queries) {
            console.log(`\n📝 搜索查询: "${query}"`);
            console.log('=' .repeat(50));
            
            // 关键词搜索
            try {
                const keywordResults = await this.searchService.keywordSearch(userId, query, {
                    limit: 3,
                    includeContent: false
                });
                
                console.log(`\n🔤 关键词搜索结果 (${keywordResults.results.length}个):`);
                if (keywordResults.results.length === 0) {
                    console.log('   无结果');
                } else {
                    keywordResults.results.forEach((result, index) => {
                        console.log(`   ${index + 1}. ${result.title}`);
                    });
                }
            } catch (error) {
                console.log('   关键词搜索失败:', error.message);
            }

            // 语义搜索 (如果可用)
            try {
                const semanticResults = await this.searchService.semanticSearch(userId, query, {
                    limit: 3,
                    threshold: 0.6,
                    includeContent: false
                });
                
                console.log(`\n🧠 语义搜索结果 (${semanticResults.results.length}个):`);
                if (semanticResults.results.length === 0) {
                    console.log('   无结果');
                } else {
                    semanticResults.results.forEach((result, index) => {
                        console.log(`   ${index + 1}. ${result.title} (相似度: ${result.similarity.toFixed(3)})`);
                        console.log(`      匹配文本: ${result.matchedText.substring(0, 80)}...`);
                    });
                }
            } catch (error) {
                console.log('   🚫 语义搜索不可用:', error.message.includes('embedding') ? '网络连接问题' : error.message);
            }
        }
    }

    async showExistingVectorData() {
        console.log('\n📊 现有向量数据分析\n');
        
        try {
            // 获取向量数据统计
            const [vectorStats] = await executeWithLog(`
                SELECT 
                    COUNT(*) as total_vectors,
                    COUNT(DISTINCT note_id) as vectorized_notes,
                    AVG(LENGTH(chunk_text)) as avg_chunk_length
                FROM note_vectors 
                WHERE user_id = 3
            `);

            console.log('向量数据统计:');
            console.log(`  - 总向量数: ${vectorStats[0].total_vectors}`);
            console.log(`  - 已向量化笔记数: ${vectorStats[0].vectorized_notes}`);
            console.log(`  - 平均分块长度: ${Math.round(vectorStats[0].avg_chunk_length)} 字符`);

            // 显示一些示例向量数据
            const [sampleVectors] = await executeWithLog(`
                SELECT 
                    nv.note_id,
                    n.title,
                    nv.chunk_index,
                    LEFT(nv.chunk_text, 100) as sample_text,
                    JSON_LENGTH(nv.embedding_vector) as vector_dimension
                FROM note_vectors nv
                JOIN notes n ON nv.note_id = n.id
                WHERE nv.user_id = 3
                ORDER BY nv.note_id, nv.chunk_index
                LIMIT 5
            `);

            console.log('\n示例向量数据:');
            sampleVectors.forEach((vector, index) => {
                console.log(`  ${index + 1}. 笔记: "${vector.title}" (ID: ${vector.note_id})`);
                console.log(`     分块 ${vector.chunk_index}: ${vector.sample_text}...`);
                console.log(`     向量维度: ${vector.vector_dimension}`);
            });

        } catch (error) {
            console.error('获取向量数据失败:', error.message);
        }
    }

    async explainSemanticSearch() {
        console.log('\n🎓 语义搜索原理解释\n');
        
        console.log('语义搜索的工作流程:');
        console.log('1. 📝 文本预处理: 清理HTML标签，分块处理');
        console.log('2. 🔢 向量化: 使用AI模型将文本转换为1536维向量');
        console.log('3. 💾 存储: 将向量存储在数据库中');
        console.log('4. 🔍 搜索: 将查询也转换为向量');
        console.log('5. 📐 计算相似度: 使用余弦相似度比较向量');
        console.log('6. 📊 排序: 按相似度排序返回结果');
        
        console.log('\n语义搜索的优势:');
        console.log('✅ 理解内容含义，不仅仅是关键词匹配');
        console.log('✅ 可以找到语义相关但词汇不同的内容');
        console.log('✅ 支持自然语言查询');
        console.log('✅ 能处理同义词、近义词');
        
        console.log('\n当前系统状态:');
        console.log('🔧 数据库表: 已创建完整的知识库表结构');
        console.log('📊 向量数据: 已有部分笔记的向量数据');
        console.log('🤖 AI服务: Gemini API已配置但网络连接有问题');
        console.log('🔄 降级机制: 语义搜索失败时自动切换到关键词搜索');
    }

    async run() {
        console.log('🧠 智能知识库 - 语义搜索演示\n');
        
        await this.explainSemanticSearch();
        await this.showExistingVectorData();
        await this.demonstrateSearchDifference();
        
        console.log('\n💡 使用建议:');
        console.log('1. 检查网络连接和代理设置');
        console.log('2. 确认Gemini API密钥有效');
        console.log('3. 在网络问题解决前，可以使用关键词搜索');
        console.log('4. 系统会自动在语义搜索失败时降级到关键词搜索');
        
        process.exit(0);
    }
}

// 运行演示
if (require.main === module) {
    const demo = new SemanticSearchDemo();
    demo.run().catch(console.error);
}

module.exports = SemanticSearchDemo;
