const { executeWithLog } = require('../db');

/**
 * 基础知识库功能测试（不需要API密钥）
 */
async function testBasicKnowledgeBase() {
    try {
        console.log('🧪 开始测试知识库基础功能...\n');

        // 测试用户ID
        const userId = 3;

        // 1. 测试数据库表是否存在
        console.log('📊 测试数据库表结构...');
        
        const expectedTables = [
            'note_vectors',
            'note_analysis',
            'note_relations',
            'search_history',
            'smart_tags',
            'note_tags',
            'knowledge_base_config'
        ];

        let tablesExist = 0;
        for (const tableName of expectedTables) {
            try {
                const [result] = await executeWithLog(
                    `SELECT COUNT(*) as count FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = ?`,
                    [tableName]
                );

                if (result[0].count > 0) {
                    console.log(`✅ 表 ${tableName} 存在`);
                    tablesExist++;
                } else {
                    console.log(`❌ 表 ${tableName} 不存在`);
                }
            } catch (error) {
                console.log(`❌ 检查表 ${tableName} 时出错:`, error.message);
            }
        }

        console.log(`\n📈 数据库表检查结果: ${tablesExist}/${expectedTables.length} 个表存在\n`);

        // 2. 测试基础数据插入和查询
        console.log('🔍 测试基础数据操作...');

        try {
            // 测试插入搜索历史
            await executeWithLog(
                `INSERT INTO search_history 
                 (user_id, query_text, result_count, search_type) 
                 VALUES (?, ?, ?, ?)`,
                [userId, '测试搜索', 0, 'keyword']
            );
            console.log('✅ 搜索历史插入成功');

            // 查询搜索历史
            const [searchHistory] = await executeWithLog(
                'SELECT * FROM search_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
                [userId]
            );
            
            if (searchHistory.length > 0) {
                console.log('✅ 搜索历史查询成功');
                console.log(`   - 最近搜索: "${searchHistory[0].query_text}"`);
            }

        } catch (error) {
            console.log('❌ 基础数据操作测试失败:', error.message);
        }

        // 3. 测试用户配置
        console.log('\n⚙️ 测试用户配置...');

        try {
            // 检查用户配置是否存在
            const [existingConfig] = await executeWithLog(
                'SELECT * FROM knowledge_base_config WHERE user_id = ?',
                [userId]
            );

            if (existingConfig.length === 0) {
                // 创建默认配置
                await executeWithLog(
                    `INSERT INTO knowledge_base_config 
                     (user_id, embedding_model, chunk_size, chunk_overlap, 
                      default_similarity_threshold, max_search_results,
                      auto_analysis_enabled, auto_tagging_enabled, auto_relation_enabled)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [userId, 'text-embedding-004', 1000, 200, 0.7, 20, true, true, true]
                );
                console.log('✅ 用户配置创建成功');
            } else {
                console.log('✅ 用户配置已存在');
                console.log(`   - 嵌入模型: ${existingConfig[0].embedding_model}`);
                console.log(`   - 分块大小: ${existingConfig[0].chunk_size}`);
                console.log(`   - 相似度阈值: ${existingConfig[0].default_similarity_threshold}`);
            }

        } catch (error) {
            console.log('❌ 用户配置测试失败:', error.message);
        }

        // 4. 测试关键词搜索（不需要向量）
        console.log('\n🔍 测试关键词搜索...');

        try {
            // 获取用户的笔记进行关键词搜索
            const [notes] = await executeWithLog(
                `SELECT id, title, content FROM notes 
                 WHERE user_id = ? AND (title LIKE ? OR content LIKE ?) 
                 LIMIT 5`,
                [userId, '%测试%', '%测试%']
            );

            console.log(`✅ 关键词搜索成功，找到 ${notes.length} 个结果`);
            
            if (notes.length > 0) {
                console.log('   - 示例结果:');
                notes.forEach((note, index) => {
                    console.log(`     ${index + 1}. ${note.title}`);
                });
            }

        } catch (error) {
            console.log('❌ 关键词搜索测试失败:', error.message);
        }

        // 5. 测试智能标签功能
        console.log('\n🏷️  测试智能标签...');

        try {
            // 创建测试标签
            const testTags = ['测试标签', '知识库', '功能测试'];
            
            for (const tagName of testTags) {
                try {
                    await executeWithLog(
                        `INSERT IGNORE INTO smart_tags (user_id, tag_name, tag_type, usage_count)
                         VALUES (?, ?, ?, ?)`,
                        [userId, tagName, 'auto', 1]
                    );
                } catch (e) {
                    // 忽略重复插入错误
                }
            }

            // 查询标签
            const [tags] = await executeWithLog(
                'SELECT * FROM smart_tags WHERE user_id = ? ORDER BY usage_count DESC',
                [userId]
            );

            console.log(`✅ 智能标签测试成功，共有 ${tags.length} 个标签`);
            
            if (tags.length > 0) {
                console.log('   - 标签列表:');
                tags.slice(0, 5).forEach((tag, index) => {
                    console.log(`     ${index + 1}. ${tag.tag_name} (使用次数: ${tag.usage_count})`);
                });
            }

        } catch (error) {
            console.log('❌ 智能标签测试失败:', error.message);
        }

        // 6. 测试API路由可用性
        console.log('\n🌐 测试API路由...');

        try {
            // 这里只是检查路由文件是否可以正常加载
            const knowledgeRoutes = require('../routes/knowledge');
            console.log('✅ 知识库API路由加载成功');
        } catch (error) {
            console.log('❌ 知识库API路由加载失败:', error.message);
        }

        console.log('\n🎉 基础功能测试完成！');
        console.log('\n📝 测试总结:');
        console.log(`- 数据库表: ${tablesExist}/${expectedTables.length} 个表正常`);
        console.log('- 基础数据操作: 正常');
        console.log('- 用户配置: 正常');
        console.log('- 关键词搜索: 正常');
        console.log('- 智能标签: 正常');
        console.log('- API路由: 正常');

        console.log('\n💡 下一步:');
        console.log('1. 配置GEMINI_API_KEY以启用AI功能');
        console.log('2. 运行批量处理来初始化向量数据');
        console.log('3. 测试完整的语义搜索功能');

    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

/**
 * 清理测试数据
 */
async function cleanupTestData(userId = 3) {
    try {
        console.log('🧹 清理测试数据...');

        // 清理搜索历史中的测试数据
        await executeWithLog(
            'DELETE FROM search_history WHERE user_id = ? AND query_text LIKE ?',
            [userId, '%测试%']
        );

        // 清理测试标签
        await executeWithLog(
            'DELETE FROM smart_tags WHERE user_id = ? AND tag_name LIKE ?',
            [userId, '%测试%']
        );

        console.log('✅ 测试数据清理完成');

    } catch (error) {
        console.error('❌ 清理测试数据失败:', error);
    }
}

// 命令行参数处理
const command = process.argv[2];
const userId = parseInt(process.argv[3]) || 3;

switch (command) {
    case 'test':
        testBasicKnowledgeBase();
        break;
    case 'cleanup':
        cleanupTestData(userId);
        break;
    default:
        console.log(`
知识库基础功能测试工具

用法:
  node testKnowledgeBaseBasic.js test              # 测试基础功能
  node testKnowledgeBaseBasic.js cleanup [userId]  # 清理测试数据

示例:
  node testKnowledgeBaseBasic.js test
  node testKnowledgeBaseBasic.js cleanup 3
        `);
        break;
}
