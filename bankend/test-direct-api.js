const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');
require('dotenv').config();

async function testDirectAPI() {
    try {
        console.log('Testing Direct Gemini API...');
        
        if (!process.env.GEMINI_API_KEY) {
            console.error('GEMINI_API_KEY not found in environment');
            return;
        }
        
        console.log('API Key found:', process.env.GEMINI_API_KEY.substring(0, 10) + '...');
        
        // 配置代理
        const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
        const agent = new HttpsProxyAgent(proxyUrl);
        console.log('Using proxy:', proxyUrl);
        
        // 直接调用API
        const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${process.env.GEMINI_API_KEY}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: "Hello, how are you? Please respond in Chinese."
                }]
            }]
        };
        
        console.log('Making API request...');
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
            agent: agent
        });
        
        console.log('Response status:', response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error:', errorText);
            return;
        }
        
        const data = await response.json();
        console.log('API Response:', JSON.stringify(data, null, 2));
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const text = data.candidates[0].content.parts[0].text;
            console.log('Generated text:', text);
        }
        
    } catch (error) {
        console.error('Error:', error.message);
        console.error('Full error:', error);
    }
}

testDirectAPI();
