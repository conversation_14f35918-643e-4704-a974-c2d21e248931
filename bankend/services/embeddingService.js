const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');
const crypto = require('crypto');
const { executeWithLog } = require('../db');

class EmbeddingService {
    constructor() {
        this.isAvailable = false;
        this.genAI = null;
        this.model = null;

        if (process.env.GEMINI_API_KEY && process.env.GEMINI_API_KEY !== 'AIzaSyCxcYEGemEa3nbKU1JwByF0hdq_RYP04_g') {
            try {
                this.apiKey = process.env.GEMINI_API_KEY;
                this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

                // 配置代理
                const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
                this.agent = new HttpsProxyAgent(proxyUrl);

                this.isAvailable = true;
                console.log('Gemini AI embedding service initialized with proxy support');
            } catch (error) {
                console.warn('Failed to initialize Gemini AI:', error.message);
                this.isAvailable = false;
            }
        } else {
            console.warn('GEMINI_API_KEY not configured, embedding service will be disabled');
        }
        
        // 文本分块配置
        this.chunkSize = 1000; // 每块最大字符数
        this.chunkOverlap = 200; // 块之间的重叠字符数
    }

    /**
     * 将文本分块处理
     * @param {string} text - 要分块的文本
     * @returns {Array} 文本块数组
     */
    chunkText(text) {
        if (!text || text.length <= this.chunkSize) {
            return [text || ''];
        }

        const chunks = [];
        let start = 0;

        while (start < text.length) {
            let end = start + this.chunkSize;
            
            // 如果不是最后一块，尝试在句号、换行符或空格处分割
            if (end < text.length) {
                const searchEnd = Math.min(end + 100, text.length);
                const breakPoints = [
                    text.lastIndexOf('。', searchEnd),
                    text.lastIndexOf('\n', searchEnd),
                    text.lastIndexOf(' ', searchEnd)
                ];
                
                const bestBreak = Math.max(...breakPoints.filter(pos => pos > start + this.chunkSize * 0.8));
                if (bestBreak > start) {
                    end = bestBreak + 1;
                }
            }

            chunks.push(text.slice(start, end).trim());
            start = end - this.chunkOverlap;
        }

        return chunks.filter(chunk => chunk.length > 0);
    }

    /**
     * 生成文本的向量表示
     * @param {string} text - 要向量化的文本
     * @returns {Promise<Array>} 向量数组
     */
    async generateEmbedding(text) {
        if (!this.isAvailable) {
            throw new Error('Embedding service is not available. Please configure GEMINI_API_KEY.');
        }

        try {
            if (!text || text.trim().length === 0) {
                throw new Error('Text cannot be empty');
            }

            // 使用直接API调用（带重试机制）
            return await this.generateEmbeddingWithRetry(text.trim());
        } catch (error) {
            console.error('Error generating embedding:', error);
            throw new Error(`Failed to generate embedding: ${error.message}`);
        }
    }

    /**
     * 计算内容哈希
     * @param {string} content - 内容
     * @returns {string} SHA-256哈希值
     */
    generateContentHash(content) {
        return crypto.createHash('sha256').update(content).digest('hex');
    }

    /**
     * 为笔记内容生成向量并存储
     * @param {number} noteId - 笔记ID
     * @param {number} userId - 用户ID
     * @param {string} content - 笔记内容
     * @returns {Promise<Object>} 处理结果
     */
    async processNoteContent(noteId, userId, content) {
        try {
            // 清理HTML标签，提取纯文本
            const cleanContent = this.cleanHtmlContent(content);
            const contentHash = this.generateContentHash(cleanContent);

            // 检查是否已经处理过相同内容
            const [existingVectors] = await executeWithLog(
                'SELECT id FROM note_vectors WHERE note_id = ? AND content_hash = ?',
                [noteId, contentHash]
            );

            if (existingVectors.length > 0) {
                console.log(`Note ${noteId} content unchanged, skipping vectorization`);
                return { status: 'unchanged', vectorCount: existingVectors.length };
            }

            // 删除旧的向量数据
            await executeWithLog(
                'DELETE FROM note_vectors WHERE note_id = ?',
                [noteId]
            );

            // 分块处理内容
            const chunks = this.chunkText(cleanContent);
            const vectors = [];

            console.log(`Processing ${chunks.length} chunks for note ${noteId}`);

            // 为每个块生成向量
            for (let i = 0; i < chunks.length; i++) {
                const chunk = chunks[i];
                
                try {
                    const embedding = await this.generateEmbedding(chunk);
                    
                    // 存储向量
                    const [result] = await executeWithLog(
                        `INSERT INTO note_vectors 
                         (note_id, user_id, content_hash, embedding_vector, chunk_index, chunk_text) 
                         VALUES (?, ?, ?, ?, ?, ?)`,
                        [
                            noteId,
                            userId,
                            contentHash,
                            JSON.stringify(embedding),
                            i,
                            chunk
                        ]
                    );

                    vectors.push({
                        id: result.insertId,
                        chunkIndex: i,
                        chunkText: chunk.substring(0, 100) + '...'
                    });

                    // 添加延迟避免API限制
                    if (i < chunks.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                } catch (error) {
                    console.error(`Error processing chunk ${i} for note ${noteId}:`, error);
                    // 继续处理其他块
                }
            }

            console.log(`Successfully processed ${vectors.length} vectors for note ${noteId}`);

            return {
                status: 'success',
                vectorCount: vectors.length,
                chunkCount: chunks.length,
                contentHash,
                vectors
            };

        } catch (error) {
            console.error(`Error processing note content for note ${noteId}:`, error);
            throw error;
        }
    }

    /**
     * 清理HTML内容，提取纯文本
     * @param {string} htmlContent - HTML内容
     * @returns {string} 纯文本内容
     */
    cleanHtmlContent(htmlContent) {
        if (!htmlContent) return '';

        // 简单的HTML标签清理
        return htmlContent
            .replace(/<script[^>]*>.*?<\/script>/gis, '') // 移除script标签
            .replace(/<style[^>]*>.*?<\/style>/gis, '') // 移除style标签
            .replace(/<[^>]+>/g, ' ') // 移除所有HTML标签
            .replace(/&nbsp;/g, ' ') // 替换HTML实体
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/\s+/g, ' ') // 合并多个空格
            .trim();
    }

    /**
     * 计算两个向量的余弦相似度
     * @param {Array} vector1 - 向量1
     * @param {Array} vector2 - 向量2
     * @returns {number} 相似度分数 (0-1)
     */
    calculateCosineSimilarity(vector1, vector2) {
        if (vector1.length !== vector2.length) {
            throw new Error('Vectors must have the same length');
        }

        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;

        for (let i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        if (norm1 === 0 || norm2 === 0) {
            return 0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 批量处理用户的所有笔记
     * @param {number} userId - 用户ID
     * @returns {Promise<Object>} 处理结果统计
     */
    async processAllUserNotes(userId) {
        try {
            // 获取用户所有笔记
            const [notes] = await executeWithLog(
                'SELECT id, title, content FROM notes WHERE user_id = ? ORDER BY updated_at DESC',
                [userId]
            );

            console.log(`Processing ${notes.length} notes for user ${userId}`);

            const results = {
                total: notes.length,
                processed: 0,
                skipped: 0,
                errors: 0,
                details: []
            };

            for (const note of notes) {
                try {
                    const result = await this.processNoteContent(note.id, userId, note.content);
                    
                    if (result.status === 'success') {
                        results.processed++;
                    } else if (result.status === 'unchanged') {
                        results.skipped++;
                    }

                    results.details.push({
                        noteId: note.id,
                        title: note.title,
                        status: result.status,
                        vectorCount: result.vectorCount
                    });

                } catch (error) {
                    console.error(`Error processing note ${note.id}:`, error);
                    results.errors++;
                    results.details.push({
                        noteId: note.id,
                        title: note.title,
                        status: 'error',
                        error: error.message
                    });
                }

                // 添加延迟避免API限制
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            console.log(`Batch processing complete for user ${userId}:`, results);
            return results;

        } catch (error) {
            console.error(`Error in batch processing for user ${userId}:`, error);
            throw error;
        }
    }

    // 重试机制的辅助函数
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 带重试机制的embedding生成
    async generateEmbeddingWithRetry(text, maxRetries = 3) {
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const url = `${this.baseUrl}/models/text-embedding-004:embedContent?key=${this.apiKey}`;

                const requestBody = {
                    content: {
                        parts: [{ text: text }]
                    }
                };

                console.log(`[EMBEDDING] 尝试第 ${attempt} 次生成embedding...`);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                    agent: this.agent,
                    timeout: 30000
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    const error = new Error(`API Error: ${response.status} - ${errorText}`);

                    // 检查是否是可重试的错误
                    if (response.status === 503 || response.status === 429 || response.status >= 500) {
                        console.log(`[EMBEDDING] 第 ${attempt} 次请求失败 (${response.status})，${attempt < maxRetries ? '准备重试' : '已达到最大重试次数'}`);
                        lastError = error;

                        if (attempt < maxRetries) {
                            const delay = Math.pow(2, attempt) * 1000;
                            console.log(`[EMBEDDING] 等待 ${delay}ms 后重试...`);
                            await this.sleep(delay);
                            continue;
                        }
                    }

                    throw error;
                }

                const data = await response.json();

                if (data.embedding && data.embedding.values) {
                    console.log(`[EMBEDDING] 第 ${attempt} 次请求成功`);
                    return data.embedding.values;
                } else {
                    throw new Error('No valid embedding response from API');
                }

            } catch (error) {
                console.error(`[EMBEDDING] 第 ${attempt} 次请求出错:`, error.message);
                lastError = error;

                if ((error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.message.includes('timeout')) && attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000;
                    console.log(`[EMBEDDING] 网络错误，等待 ${delay}ms 后重试...`);
                    await this.sleep(delay);
                    continue;
                }

                if (attempt === maxRetries) {
                    break;
                }
            }
        }

        console.error(`[EMBEDDING] 所有重试都失败了，最后的错误:`, lastError?.message);
        throw new Error(`Failed to generate embedding after ${maxRetries} attempts: ${lastError?.message}`);
    }
}

module.exports = EmbeddingService;
