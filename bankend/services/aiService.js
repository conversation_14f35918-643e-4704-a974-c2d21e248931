const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');
require('dotenv').config();

class AIService {
    constructor() {
        if (process.env.GEMINI_API_KEY) {
            try {
                this.apiKey = process.env.GEMINI_API_KEY;
                this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
                this.model = 'gemini-2.0-flash';

                // 配置代理
                const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
                this.agent = new HttpsProxyAgent(proxyUrl);

                this.generationConfig = {
                    maxOutputTokens: 2048,
                    temperature: 0.7,
                    topP: 0.8,
                    topK: 40,
                };

                this.available = true;
                console.log("Gemini AI Service Initialized Successfully with proxy support.");
            } catch (error) {
                console.error("Failed to initialize Gemini AI Service:", error);
                this.available = false;
            }
        } else {
            console.warn("GEMINI_API_KEY not configured. AI Service is disabled.");
            this.available = false;
        }
    }

    isAvailable() {
        return this.available;
    }

    getModelInfo() {
        return {
            available: this.isAvailable(),
            model: this.isAvailable() ? this.model : null,
        };
    }

    createChat(history = []) {
        if (!this.isAvailable()) {
            throw new Error("AI Service is not available.");
        }

        // 返回一个聊天会话对象
        return {
            history: history,
            sendMessage: async (message) => {
                return await this.generateContent(message, this.history);
            },
            sendMessageStream: async (message) => {
                return await this.generateContentStream(message, this.history);
            }
        };
    }

    // 保持向后兼容
    startChat(history = []) {
        return this.createChat(history);
    }

    // 重试机制的辅助函数
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 直接生成内容（带重试机制）
    async generateContent(prompt, history = [], maxRetries = 3) {
        if (!this.isAvailable()) {
            throw new Error("AI Service is not available.");
        }

        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const url = `${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`;

                // 构建请求内容
                const contents = [];

                // 添加历史对话
                history.forEach(item => {
                    contents.push({
                        role: item.role === 'user' ? 'user' : 'model',
                        parts: [{ text: item.parts?.[0]?.text || item.text || item }]
                    });
                });

                // 添加当前消息
                contents.push({
                    role: 'user',
                    parts: [{ text: prompt }]
                });

                const requestBody = {
                    contents: contents,
                    generationConfig: this.generationConfig
                };

                console.log(`[AI] 尝试第 ${attempt} 次请求 Gemini API...`);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                    agent: this.agent,
                    timeout: 30000 // 30秒超时
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    const error = new Error(`API Error: ${response.status} - ${errorText}`);

                    // 检查是否是可重试的错误
                    if (response.status === 503 || response.status === 429 || response.status >= 500) {
                        console.log(`[AI] 第 ${attempt} 次请求失败 (${response.status})，${attempt < maxRetries ? '准备重试' : '已达到最大重试次数'}`);
                        lastError = error;

                        if (attempt < maxRetries) {
                            // 指数退避：2^attempt * 1000ms
                            const delay = Math.pow(2, attempt) * 1000;
                            console.log(`[AI] 等待 ${delay}ms 后重试...`);
                            await this.sleep(delay);
                            continue;
                        }
                    }

                    throw error;
                }

                const data = await response.json();

                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                    const text = data.candidates[0].content.parts[0].text;
                    console.log(`[AI] 第 ${attempt} 次请求成功`);
                    return {
                        response: {
                            text: () => text,
                            groundingMetadata: data.candidates[0].groundingMetadata
                        }
                    };
                } else {
                    throw new Error('No valid response from API');
                }

            } catch (error) {
                console.error(`[AI] 第 ${attempt} 次请求出错:`, error.message);
                lastError = error;

                // 如果是网络错误或超时，可以重试
                if ((error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.message.includes('timeout')) && attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000;
                    console.log(`[AI] 网络错误，等待 ${delay}ms 后重试...`);
                    await this.sleep(delay);
                    continue;
                }

                // 其他错误直接抛出
                if (attempt === maxRetries) {
                    break;
                }
            }
        }

        console.error(`[AI] 所有重试都失败了，最后的错误:`, lastError?.message);
        throw new Error(`Failed to generate content after ${maxRetries} attempts: ${lastError?.message}`);
    }

    async generateEmbedding(text, maxRetries = 3) {
        if (!this.isAvailable()) {
            throw new Error("Embedding service is not available. Please configure GEMINI_API_KEY.");
        }

        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const url = `${this.baseUrl}/models/text-embedding-004:embedContent?key=${this.apiKey}`;

                const requestBody = {
                    content: {
                        parts: [{ text: text }]
                    }
                };

                console.log(`[AI] 尝试第 ${attempt} 次生成embedding...`);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                    agent: this.agent,
                    timeout: 30000
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    const error = new Error(`API Error: ${response.status} - ${errorText}`);

                    // 检查是否是可重试的错误
                    if (response.status === 503 || response.status === 429 || response.status >= 500) {
                        console.log(`[AI] Embedding第 ${attempt} 次请求失败 (${response.status})，${attempt < maxRetries ? '准备重试' : '已达到最大重试次数'}`);
                        lastError = error;

                        if (attempt < maxRetries) {
                            const delay = Math.pow(2, attempt) * 1000;
                            console.log(`[AI] 等待 ${delay}ms 后重试embedding...`);
                            await this.sleep(delay);
                            continue;
                        }
                    }

                    throw error;
                }

                const data = await response.json();

                if (data.embedding && data.embedding.values) {
                    console.log(`[AI] Embedding第 ${attempt} 次请求成功`);
                    return data.embedding.values;
                } else {
                    throw new Error('No valid embedding response from API');
                }

            } catch (error) {
                console.error(`[AI] Embedding第 ${attempt} 次请求出错:`, error.message);
                lastError = error;

                if ((error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.message.includes('timeout')) && attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000;
                    console.log(`[AI] Embedding网络错误，等待 ${delay}ms 后重试...`);
                    await this.sleep(delay);
                    continue;
                }

                if (attempt === maxRetries) {
                    break;
                }
            }
        }

        console.error(`[AI] Embedding所有重试都失败了，最后的错误:`, lastError?.message);
        throw new Error(`Failed to generate embedding after ${maxRetries} attempts: ${lastError?.message}`);
    }

    async analyzeContent(prompt) {
        if (!this.isAvailable()) {
            throw new Error("Analysis service is not available. Please configure GEMINI_API_KEY.");
        }
        try {
            const result = await this.generateContent(prompt);
            return result.response.text();
        } catch (error) {
            console.error("Error analyzing content:", error);
            throw new Error("Failed to analyze content.");
        }
    }

    // 流式生成内容
    async generateContentStream(prompt, onChunk, history = []) {
        if (!this.isAvailable()) {
            throw new Error("AI Service is not available.");
        }
        try {
            // 注意：当前的直接API调用不支持流式，这里模拟流式输出
            const result = await this.generateContent(prompt, history);
            const text = result.response.text();

            // 模拟流式输出，将文本分块发送
            const chunks = text.split('');
            for (let i = 0; i < chunks.length; i += 5) {
                const chunk = chunks.slice(i, i + 5).join('');
                onChunk(chunk);
                // 添加小延迟模拟流式效果
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        } catch (error) {
            console.error('AI生成内容时出错:', error);
            throw new Error('AI服务暂时不可用，请稍后重试');
        }
    }

    // 发送消息到聊天会话（流式，支持Google搜索）
    async sendMessageStream(chat, message, onChunk, onSearchInfo) {
        try {
            await this.generateContentStream(message, onChunk, chat.history);
        } catch (error) {
            console.error('发送消息时出错:', error);
            throw new Error('发送消息失败，请稍后重试');
        }
    }
}

// Singleton instance
const aiServiceInstance = new AIService();
module.exports = aiServiceInstance;
