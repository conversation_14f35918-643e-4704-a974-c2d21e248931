const aiService = require('./aiService');
const { executeWithLog } = require('../db');
const EmbeddingService = require('./embeddingService');

class AnalysisService {
    constructor() {
        this.embeddingService = new EmbeddingService();
        if (aiService.isAvailable()) {
            console.log('AI analysis service is enabled.');
        } else {
            console.warn('AI analysis service is disabled due to missing API key.');
        }
    }

    async analyzeNoteContent(noteId, userId, title, content) {
        if (!aiService.isAvailable()) {
            throw new Error('Analysis service is not available. Please configure GEMINI_API_KEY.');
        }

        try {
            console.log(`Analyzing note ${noteId} for user ${userId}`);

            const cleanContent = this.embeddingService.cleanHtmlContent(content);
            const fullText = `${title}\n\n${cleanContent}`;

            if (fullText.trim().length < 10) {
                console.log(`Note ${noteId} content too short, skipping analysis`);
                return null;
            }

            const analysisPrompt = `
请分析以下笔记内容，并以JSON格式返回分析结果：

标题：${title}
内容：${cleanContent}

请提供以下分析：
1. summary: 生成一个简洁的摘要（50-100字）
2. keywords: 提取5-10个关键词（数组格式）
3. topics: 识别2-5个主题标签（数组格式）
4. sentiment_score: 情感分数（-1到1之间的数字，-1最负面，0中性，1最正面）
5. complexity_score: 内容复杂度（0到1之间的数字，0最简单，1最复杂）
6. category: 推荐的分类（如：工作、学习、生活、技术、想法等）
7. confidence_score: 分类的置信度（0到1之间的数字）

请确保返回有效的JSON格式，不要包含其他文本。
`;

            const responseText = await aiService.analyzeContent(analysisPrompt);
            
            let analysisResult;
            try {
                const jsonMatch = responseText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    analysisResult = JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('No JSON found in response');
                }
            } catch (parseError) {
                console.error('Error parsing AI analysis response:', parseError);
                console.log('Raw response:', responseText);
                
                analysisResult = {
                    summary: cleanContent.substring(0, 100) + '...',
                    keywords: this.extractSimpleKeywords(cleanContent),
                    topics: ['未分类'],
                    sentiment_score: 0,
                    complexity_score: 0.5,
                    category: '其他',
                    confidence_score: 0.3
                };
            }

            const cleanedResult = this.validateAnalysisResult(analysisResult);
            const stats = this.calculateContentStats(cleanContent);

            await this.saveAnalysisResult(noteId, userId, cleanedResult, stats);

            console.log(`Analysis completed for note ${noteId}`);

            return {
                noteId,
                ...cleanedResult,
                ...stats,
                analyzedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error(`Error analyzing note ${noteId}:`, error);

            // 如果是API过载错误，返回一个默认的分析结果而不是抛出错误
            if (error.message.includes('503') || error.message.includes('overloaded') || error.message.includes('UNAVAILABLE')) {
                console.log(`Note ${noteId}: API过载，返回默认分析结果`);
                const stats = this.calculateBasicStats(content);
                return {
                    summary: '由于AI服务暂时过载，无法生成详细摘要。',
                    keywords: this.extractSimpleKeywords(content),
                    topics: ['未分类'],
                    sentiment: 'neutral',
                    importance: 'medium',
                    ...stats,
                    analyzedAt: new Date().toISOString()
                };
            }

            throw new Error(`Analysis failed: ${error.message}`);
        }
    }
    validateAnalysisResult(result) {
        return {
            summary: typeof result.summary === 'string' ? 
                result.summary.substring(0, 500) : '暂无摘要',
            keywords: Array.isArray(result.keywords) ? 
                result.keywords.slice(0, 10).filter(k => typeof k === 'string') : [],
            topics: Array.isArray(result.topics) ? 
                result.topics.slice(0, 5).filter(t => typeof t === 'string') : ['未分类'],
            sentiment_score: this.clampNumber(result.sentiment_score, -1, 1, 0),
            complexity_score: this.clampNumber(result.complexity_score, 0, 1, 0.5),
            category: typeof result.category === 'string' ? 
                result.category.substring(0, 100) : '其他',
            confidence_score: this.clampNumber(result.confidence_score, 0, 1, 0.5)
        };
    }
    clampNumber(value, min, max, defaultValue) {
        const num = parseFloat(value);
        if (isNaN(num)) return defaultValue;
        return Math.max(min, Math.min(max, num));
    }
    calculateContentStats(content) {
        const charCount = content.length;
        const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
        
        const chineseChars = (content.match(/[\u4e00-\u9fa5]/g) || []).length;
        const language = chineseChars > charCount * 0.3 ? 'zh-CN' : 'en';

        return {
            word_count: wordCount,
            char_count: charCount,
            language
        };
    }
    extractSimpleKeywords(content) {
        const words = content
            .toLowerCase()
            .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 1);
        const wordCount = {};
        words.forEach(word => {
            wordCount[word] = (wordCount[word] || 0) + 1;
        });
        return Object.entries(wordCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([word]) => word);
    }
    async saveAnalysisResult(noteId, userId, analysis, stats) {
        try {
            await executeWithLog(
                'DELETE FROM note_analysis WHERE note_id = ?',
                [noteId]
            );
            await executeWithLog(
                `INSERT INTO note_analysis \n                 (note_id, user_id, summary, keywords, topics, sentiment_score, \n                  complexity_score, word_count, char_count, language, category, confidence_score)\n                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    noteId,
                    userId,
                    analysis.summary,
                    JSON.stringify(analysis.keywords),
                    JSON.stringify(analysis.topics),
                    analysis.sentiment_score,
                    analysis.complexity_score,
                    stats.word_count,
                    stats.char_count,
                    stats.language,
                    analysis.category,
                    analysis.confidence_score
                ]
            );

            console.log(`Analysis result saved for note ${noteId}`);

        } catch (error) {
            console.error(`Error saving analysis result for note ${noteId}:`, error);
            throw error;
        }
    }
    async getNoteAnalysis(noteId, userId) {
        try {
            const [results] = await executeWithLog(
                `SELECT * FROM note_analysis WHERE note_id = ? AND user_id = ?`,
                [noteId, userId]
            );

            if (results.length === 0) {
                return null;
            }

            const analysis = results[0];
            
            return {
                noteId: analysis.note_id,
                summary: analysis.summary,
                keywords: this.safeParseJSON(analysis.keywords, []),
                topics: this.safeParseJSON(analysis.topics, []),
                sentimentScore: analysis.sentiment_score,
                complexityScore: analysis.complexity_score,
                wordCount: analysis.word_count,
                charCount: analysis.char_count,
                language: analysis.language,
                category: analysis.category,
                confidenceScore: analysis.confidence_score,
                createdAt: analysis.created_at,
                updatedAt: analysis.updated_at,
                analyzedAt: analysis.updated_at // 添加分析时间字段
            };

        } catch (error) {
            console.error(`Error getting note analysis for note ${noteId}:`, error);
            return null;
        }
    }
    async analyzeAllUserNotes(userId) {
        try {
            const [notes] = await executeWithLog(
                'SELECT id, title, content FROM notes WHERE user_id = ? ORDER BY updated_at DESC',
                [userId]
            );

            console.log(`Analyzing ${notes.length} notes for user ${userId}`);

            const results = {
                total: notes.length,
                analyzed: 0,
                skipped: 0,
                errors: 0,
                details: []
            };

            for (const note of notes) {
                try {
                    const existing = await this.getNoteAnalysis(note.id, userId);
                    if (existing) {
                        results.skipped++;
                        results.details.push({
                            noteId: note.id,
                            title: note.title,
                            status: 'skipped',
                            reason: 'already_analyzed'
                        });
                        continue;
                    }

                    const analysis = await this.analyzeNoteContent(
                        note.id, 
                        userId, 
                        note.title, 
                        note.content
                    );

                    if (analysis) {
                        results.analyzed++;
                        results.details.push({
                            noteId: note.id,
                            title: note.title,
                            status: 'success',
                            category: analysis.category,
                            keywords: analysis.keywords.slice(0, 3)
                        });
                    } else {
                        results.skipped++;
                        results.details.push({
                            noteId: note.id,
                            title: note.title,
                            status: 'skipped',
                            reason: 'content_too_short'
                        });
                    }

                    await new Promise(resolve => setTimeout(resolve, 1000));

                } catch (error) {
                    console.error(`Error analyzing note ${note.id}:`, error);
                    results.errors++;
                    results.details.push({
                        noteId: note.id,
                        title: note.title,
                        status: 'error',
                        error: error.message
                    });
                }
            }

            console.log(`Batch analysis complete for user ${userId}:`, results);
            return results;

        } catch (error) {
            console.error(`Error in batch analysis for user ${userId}:`, error);
            throw error;
        }
    }
    async getUserAnalysisStats(userId) {
        try {
            const [stats] = await executeWithLog(`
                SELECT 
                    COUNT(*) as total_analyzed,
                    AVG(sentiment_score) as avg_sentiment,
                    AVG(complexity_score) as avg_complexity,
                    AVG(word_count) as avg_word_count,
                    category,
                    COUNT(*) as category_count
                FROM note_analysis 
                WHERE user_id = ?
                GROUP BY category
                ORDER BY category_count DESC
            `, [userId]);

            const [topKeywords] = await executeWithLog(`
                SELECT keywords FROM note_analysis WHERE user_id = ?
            `, [userId]);

            const keywordFreq = {};
            topKeywords.forEach(row => {
                try {
                    const keywords = JSON.parse(row.keywords || '[]');
                    keywords.forEach(keyword => {
                        keywordFreq[keyword] = (keywordFreq[keyword] || 0) + 1;
                    });
                } catch (e) {
                }
            });

            const sortedKeywords = Object.entries(keywordFreq)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 20)
                .map(([keyword, count]) => ({ keyword, count }));

            return {
                totalAnalyzed: stats.length > 0 ? stats[0].total_analyzed : 0,
                avgSentiment: stats.length > 0 ? stats[0].avg_sentiment : 0,
                avgComplexity: stats.length > 0 ? stats[0].avg_complexity : 0,
                avgWordCount: stats.length > 0 ? stats[0].avg_word_count : 0,
                categories: stats.map(s => ({
                    category: s.category,
                    count: s.category_count
                })),
                topKeywords: sortedKeywords
            };

        } catch (error) {
            console.error(`Error getting analysis stats for user ${userId}:`, error);
            return {
                totalAnalyzed: 0,
                avgSentiment: 0,
                avgComplexity: 0,
                avgWordCount: 0,
                categories: [],
                topKeywords: []
            };
        }
    }

    // 安全的JSON解析方法
    safeParseJSON(jsonString, defaultValue = []) {
        if (!jsonString) {
            return defaultValue;
        }

        try {
            // 如果已经是数组，直接返回
            if (Array.isArray(jsonString)) {
                return jsonString;
            }

            // 尝试解析JSON
            const parsed = JSON.parse(jsonString);
            return Array.isArray(parsed) ? parsed : defaultValue;

        } catch (error) {
            console.log(`JSON解析失败，使用默认值: ${jsonString}`);

            // 如果JSON解析失败，尝试简单的字符串分割
            if (typeof jsonString === 'string') {
                // 移除可能的JSON格式字符
                const cleaned = jsonString.replace(/[\[\]"]/g, '');

                // 按逗号分割
                const items = cleaned.split(',')
                    .map(item => item.trim())
                    .filter(item => item.length > 0);

                return items.length > 0 ? items : defaultValue;
            }

            return defaultValue;
        }
    }

    // 计算基本统计信息
    calculateBasicStats(content) {
        const words = content.split(/\s+/).filter(word => word.length > 0);
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);

        return {
            wordCount: words.length,
            sentenceCount: sentences.length,
            paragraphCount: paragraphs.length,
            readingTime: Math.ceil(words.length / 200) // 假设每分钟200词
        };
    }

    // 简单的关键词提取（当AI服务不可用时使用）
    extractSimpleKeywords(content) {
        // 移除标点符号和特殊字符
        const cleanContent = content.replace(/[^\w\s\u4e00-\u9fff]/g, ' ');
        const words = cleanContent.split(/\s+/).filter(word => word.length > 1);

        // 统计词频
        const wordCount = {};
        words.forEach(word => {
            const lowerWord = word.toLowerCase();
            wordCount[lowerWord] = (wordCount[lowerWord] || 0) + 1;
        });

        // 过滤常见停用词
        const stopWords = new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those']);

        // 获取高频词作为关键词
        const keywords = Object.entries(wordCount)
            .filter(([word, count]) => !stopWords.has(word) && count > 1)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([word]) => word);

        return keywords.length > 0 ? keywords : ['文本', '内容'];
    }
}

module.exports = AnalysisService;
