const { executeWithLog } = require('../db');
const EmbeddingService = require('./embeddingService');
const AnalysisService = require('./analysisService');

class OrganizationService {
    constructor() {
        this.embeddingService = new EmbeddingService();
        this.analysisService = new AnalysisService();
    }

    // 安全解析JSON数据
    parseJsonSafely(jsonData, noteId = 'unknown', fieldName = 'data') {
        try {
            if (!jsonData) {
                return [];
            }

            // 如果已经是数组，直接返回
            if (Array.isArray(jsonData)) {
                return jsonData;
            }

            // 如果是字符串，尝试解析
            if (typeof jsonData === 'string') {
                // 移除可能的BOM和非打印字符
                let cleanedData = jsonData.replace(/^\uFEFF/, '').trim();

                // 如果是空字符串，返回空数组
                if (!cleanedData) {
                    return [];
                }

                // 如果不是JSON格式，可能是逗号分隔的字符串
                if (!cleanedData.startsWith('[') && !cleanedData.startsWith('{')) {
                    // 尝试作为逗号分隔的字符串处理
                    return cleanedData.split(',').map(item => item.trim()).filter(Boolean);
                }

                // 尝试JSON解析
                const parsed = JSON.parse(cleanedData);

                // 如果解析结果不是数组，转换为数组
                if (!Array.isArray(parsed)) {
                    return [parsed];
                }

                return parsed;
            }

            // 其他类型转换为数组
            return [jsonData];

        } catch (error) {
            console.error(`Error parsing ${fieldName} for note ${noteId}:`, error.message);
            console.error('Raw data (first 100 chars):',
                typeof jsonData === 'string' ? jsonData.substring(0, 100) : jsonData);

            // 如果是字符串且包含逗号，尝试作为逗号分隔处理
            if (typeof jsonData === 'string' && jsonData.includes(',')) {
                try {
                    return jsonData.split(',').map(item => item.trim()).filter(Boolean);
                } catch (splitError) {
                    console.error(`Failed to split ${fieldName} for note ${noteId}:`, splitError.message);
                }
            }

            return [];
        }
    }

    // 安全解析向量数据
    parseEmbeddingVector(vectorData, noteId = 'unknown') {
        try {
            if (!vectorData) {
                throw new Error('Empty vector data');
            }

            // 如果已经是数组，直接返回
            if (Array.isArray(vectorData)) {
                return vectorData;
            }

            // 如果是字符串，尝试解析
            if (typeof vectorData === 'string') {
                // 移除可能的BOM和非打印字符
                let cleanedData = vectorData.replace(/^\uFEFF/, '').trim();

                // 如果不是以 [ 或 { 开头，可能有前缀字符
                if (!cleanedData.startsWith('[') && !cleanedData.startsWith('{')) {
                    // 查找第一个 [ 或 {
                    const arrayStart = cleanedData.indexOf('[');
                    const objectStart = cleanedData.indexOf('{');

                    if (arrayStart !== -1 && (objectStart === -1 || arrayStart < objectStart)) {
                        cleanedData = cleanedData.substring(arrayStart);
                    } else if (objectStart !== -1) {
                        cleanedData = cleanedData.substring(objectStart);
                    }
                }

                // 如果不是以 ] 或 } 结尾，可能有后缀字符
                if (!cleanedData.endsWith(']') && !cleanedData.endsWith('}')) {
                    const arrayEnd = cleanedData.lastIndexOf(']');
                    const objectEnd = cleanedData.lastIndexOf('}');

                    if (arrayEnd !== -1 && arrayEnd > objectEnd) {
                        cleanedData = cleanedData.substring(0, arrayEnd + 1);
                    } else if (objectEnd !== -1) {
                        cleanedData = cleanedData.substring(0, objectEnd + 1);
                    }
                }

                const parsed = JSON.parse(cleanedData);

                // 验证解析结果是数组
                if (!Array.isArray(parsed)) {
                    throw new Error('Parsed data is not an array');
                }

                return parsed;
            }

            throw new Error(`Unsupported vector data type: ${typeof vectorData}`);

        } catch (error) {
            console.error(`Error parsing embedding vector for note ${noteId}:`, error.message);
            console.error('Raw data (first 100 chars):',
                typeof vectorData === 'string' ? vectorData.substring(0, 100) : vectorData);
            return null;
        }
    }

    /**
     * 发现笔记间的关联关系
     * @param {number} userId - 用户ID
     * @param {number} sourceNoteId - 源笔记ID（可选，如果不提供则分析所有笔记）
     * @returns {Promise<Object>} 关联发现结果
     */
    async discoverNoteRelations(userId, sourceNoteId = null) {
        try {
            console.log(`Discovering note relations for user ${userId}${sourceNoteId ? `, source note: ${sourceNoteId}` : ''}`);

            // 检查是否已有足够的关联数据，避免重复计算
            if (!sourceNoteId) {
                const [existingRelations] = await executeWithLog(
                    'SELECT COUNT(*) as count FROM note_relations WHERE user_id = ?',
                    [userId]
                );

                const [totalNotes] = await executeWithLog(
                    'SELECT COUNT(*) as count FROM notes WHERE user_id = ?',
                    [userId]
                );

                // 如果已有关联数据且数量合理，跳过重复计算
                const expectedRelations = Math.min(totalNotes[0].count * 2, 50); // 每个笔记最多2个关联
                if (existingRelations[0].count >= expectedRelations) {
                    console.log(`User ${userId} already has sufficient relations (${existingRelations[0].count}), skipping discovery`);
                    return {
                        analyzed: 0,
                        relationsFound: 0,
                        details: [],
                        skipped: true,
                        reason: 'Already has sufficient relations'
                    };
                }
            }

            // 获取要分析的笔记
            let notesToAnalyze;
            if (sourceNoteId) {
                // 只分析指定笔记与其他笔记的关系
                const [sourceNotes] = await executeWithLog(
                    'SELECT id, title FROM notes WHERE id = ? AND user_id = ?',
                    [sourceNoteId, userId]
                );
                if (sourceNotes.length === 0) {
                    throw new Error('Source note not found');
                }
                notesToAnalyze = sourceNotes;
            } else {
                // 分析所有笔记，但限制数量以提高性能
                const [allNotes] = await executeWithLog(
                    'SELECT id, title FROM notes WHERE user_id = ? ORDER BY updated_at DESC LIMIT 20',
                    [userId]
                );
                notesToAnalyze = allNotes;
            }

            const results = {
                analyzed: 0,
                relationsFound: 0,
                details: []
            };

            for (const note of notesToAnalyze) {
                try {
                    // 降低相似度阈值，减少计算量
                    const relations = await this.findSimilarNotes(userId, note.id, {
                        threshold: 0.8, // 提高阈值，减少结果数量
                        limit: 5, // 减少每个笔记的关联数量
                        excludeSelf: true
                    });

                    results.analyzed++;

                    for (const relation of relations) {
                        // 检查关系是否已存在
                        const [existing] = await executeWithLog(
                            `SELECT id FROM note_relations
                             WHERE user_id = ? AND source_note_id = ? AND target_note_id = ?`,
                            [userId, note.id, relation.noteId]
                        );

                        if (existing.length === 0) {
                            // 创建新关系
                            await executeWithLog(
                                `INSERT INTO note_relations
                                 (user_id, source_note_id, target_note_id, relation_type, similarity_score, relation_reason)
                                 VALUES (?, ?, ?, ?, ?, ?)`,
                                [
                                    userId,
                                    note.id,
                                    relation.noteId,
                                    'similar',
                                    relation.similarity,
                                    `基于内容相似度分析发现的关联（相似度: ${Math.round(relation.similarity * 100)}%）`
                                ]
                            );

                            results.relationsFound++;
                            results.details.push({
                                sourceNoteId: note.id,
                                sourceTitle: note.title,
                                targetNoteId: relation.noteId,
                                targetTitle: relation.title,
                                similarity: relation.similarity,
                                type: 'similar'
                            });
                        }
                    }

                    // 添加延迟避免过度使用资源
                    await new Promise(resolve => setTimeout(resolve, 100));

                } catch (error) {
                    console.error(`Error analyzing relations for note ${note.id}:`, error);
                }
            }

            console.log(`Relation discovery completed: ${results.relationsFound} new relations found`);
            return results;

        } catch (error) {
            console.error('Error in note relation discovery:', error);
            throw error;
        }
    }

    /**
     * 查找相似笔记
     * @param {number} userId - 用户ID
     * @param {number} noteId - 笔记ID
     * @param {Object} options - 选项
     * @returns {Promise<Array>} 相似笔记列表
     */
    async findSimilarNotes(userId, noteId, options = {}) {
        try {
            const {
                threshold = 0.7,
                limit = 10,
                excludeSelf = true
            } = options;

            // 获取目标笔记的向量
            const [targetVectors] = await executeWithLog(
                'SELECT embedding_vector FROM note_vectors WHERE note_id = ? AND user_id = ?',
                [noteId, userId]
            );

            if (targetVectors.length === 0) {
                return [];
            }

            // 获取所有其他笔记的向量
            let query = `
                SELECT 
                    nv.note_id,
                    nv.embedding_vector,
                    n.title
                FROM note_vectors nv
                JOIN notes n ON nv.note_id = n.id
                WHERE nv.user_id = ?
            `;
            
            const params = [userId];

            if (excludeSelf) {
                query += ' AND nv.note_id != ?';
                params.push(noteId);
            }

            const [allVectors] = await executeWithLog(query, params);

            const similarities = [];

            // 计算与每个笔记的相似度
            for (const targetVector of targetVectors) {
                const targetEmbedding = this.parseEmbeddingVector(
                    targetVector.embedding_vector,
                    targetVector.note_id
                );

                if (!targetEmbedding) {
                    continue; // 跳过无法解析的向量
                }

                for (const vector of allVectors) {
                    const embedding = this.parseEmbeddingVector(
                        vector.embedding_vector,
                        vector.note_id
                    );

                    if (!embedding) {
                        continue; // 跳过无法解析的向量
                    }

                    try {
                        const similarity = this.embeddingService.calculateCosineSimilarity(
                            targetEmbedding,
                            embedding
                        );

                        if (similarity >= threshold) {
                            // 检查是否已经有这个笔记的记录
                            const existing = similarities.find(s => s.noteId === vector.note_id);
                            if (!existing || existing.similarity < similarity) {
                                if (existing) {
                                    // 更新现有记录
                                    existing.similarity = similarity;
                                } else {
                                    // 添加新记录
                                    similarities.push({
                                        noteId: vector.note_id,
                                        title: vector.title,
                                        similarity: similarity
                                    });
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`Error processing vector for note ${vector.note_id}:`, error.message);
                        console.error('Raw vector data:', vector.embedding_vector);
                        continue;
                    }
                }
            }

            // 按相似度排序并限制结果数量
            return similarities
                .sort((a, b) => b.similarity - a.similarity)
                .slice(0, limit);

        } catch (error) {
            console.error(`Error finding similar notes for note ${noteId}:`, error);
            return [];
        }
    }

    /**
     * 自动生成和管理标签
     * @param {number} userId - 用户ID
     * @returns {Promise<Object>} 标签管理结果
     */
    async autoManageTags(userId) {
        try {
            console.log(`Auto-managing tags for user ${userId}`);

            // 检查是否已有足够的标签数据，避免重复处理
            const [existingTags] = await executeWithLog(
                'SELECT COUNT(*) as count FROM smart_tags WHERE user_id = ?',
                [userId]
            );

            const [existingTagAssignments] = await executeWithLog(
                'SELECT COUNT(*) as count FROM note_tags nt JOIN smart_tags st ON nt.tag_id = st.id WHERE st.user_id = ?',
                [userId]
            );

            // 如果已有足够的标签数据，跳过处理
            if (existingTags[0].count >= 5 && existingTagAssignments[0].count >= 10) {
                console.log(`User ${userId} already has sufficient tags (${existingTags[0].count} tags, ${existingTagAssignments[0].count} assignments), skipping auto-management`);
                return {
                    tagsCreated: 0,
                    tagsAssigned: 0,
                    notesTagged: 0,
                    skipped: true,
                    reason: 'Already has sufficient tags'
                };
            }

            // 获取所有笔记的分析结果
            const [analyses] = await executeWithLog(
                'SELECT note_id, keywords, topics, category FROM note_analysis WHERE user_id = ?',
                [userId]
            );

            const tagStats = new Map();
            const results = {
                tagsCreated: 0,
                tagsAssigned: 0,
                notesTagged: 0
            };

            // 收集所有标签并统计频率
            for (const analysis of analyses) {
                try {
                    // 使用安全解析方法处理可能损坏的JSON数据
                    const keywords = this.parseJsonSafely(analysis.keywords, analysis.note_id, 'keywords') || [];
                    const topics = this.parseJsonSafely(analysis.topics, analysis.note_id, 'topics') || [];
                    const allTags = [...keywords, ...topics, analysis.category].filter(Boolean);

                    for (const tag of allTags) {
                        if (!tagStats.has(tag)) {
                            tagStats.set(tag, {
                                count: 0,
                                notes: new Set(),
                                type: keywords.includes(tag) ? 'keyword' : 
                                      topics.includes(tag) ? 'topic' : 'category'
                            });
                        }
                        
                        const tagInfo = tagStats.get(tag);
                        tagInfo.count++;
                        tagInfo.notes.add(analysis.note_id);
                    }
                } catch (error) {
                    console.error(`Error processing analysis for note ${analysis.note_id}:`, error);
                }
            }

            // 创建或更新标签
            for (const [tagName, tagInfo] of tagStats) {
                if (tagInfo.count >= 2) { // 只创建出现2次以上的标签
                    try {
                        // 检查标签是否已存在
                        const [existingTags] = await executeWithLog(
                            'SELECT id FROM smart_tags WHERE user_id = ? AND tag_name = ?',
                            [userId, tagName]
                        );

                        let tagId;
                        if (existingTags.length === 0) {
                            // 创建新标签
                            const [result] = await executeWithLog(
                                `INSERT INTO smart_tags (user_id, tag_name, tag_type, usage_count)
                                 VALUES (?, ?, ?, ?)`,
                                [userId, tagName, 'auto', tagInfo.count]
                            );
                            tagId = result.insertId;
                            results.tagsCreated++;
                        } else {
                            // 更新现有标签
                            tagId = existingTags[0].id;
                            await executeWithLog(
                                'UPDATE smart_tags SET usage_count = ? WHERE id = ?',
                                [tagInfo.count, tagId]
                            );
                        }

                        // 为笔记分配标签
                        for (const noteId of tagInfo.notes) {
                            const [existingAssignments] = await executeWithLog(
                                'SELECT id FROM note_tags WHERE note_id = ? AND tag_id = ?',
                                [noteId, tagId]
                            );

                            if (existingAssignments.length === 0) {
                                await executeWithLog(
                                    `INSERT INTO note_tags (note_id, tag_id, confidence_score, assigned_by)
                                     VALUES (?, ?, ?, ?)`,
                                    [noteId, tagId, 0.8, 'ai']
                                );
                                results.tagsAssigned++;
                            }
                        }

                        results.notesTagged += tagInfo.notes.size;

                    } catch (error) {
                        console.error(`Error managing tag "${tagName}":`, error);
                    }
                }
            }

            console.log(`Tag management completed:`, results);
            return results;

        } catch (error) {
            console.error('Error in auto tag management:', error);
            throw error;
        }
    }

    /**
     * 智能推荐文件夹结构
     * @param {number} userId - 用户ID
     * @returns {Promise<Object>} 文件夹推荐结果
     */
    async recommendFolderStructure(userId) {
        try {
            console.log(`Generating folder structure recommendations for user ${userId}`);

            // 获取所有笔记的分析结果
            const [analyses] = await executeWithLog(
                `SELECT n.id, n.title, n.folder_id, na.category, na.topics, na.keywords
                 FROM notes n
                 LEFT JOIN note_analysis na ON n.id = na.note_id
                 WHERE n.user_id = ?`,
                [userId]
            );

            // 按分类统计
            const categoryStats = new Map();
            const topicStats = new Map();

            for (const note of analyses) {
                // 统计分类
                if (note.category) {
                    if (!categoryStats.has(note.category)) {
                        categoryStats.set(note.category, {
                            count: 0,
                            notes: [],
                            hasFolder: false
                        });
                    }
                    const catInfo = categoryStats.get(note.category);
                    catInfo.count++;
                    catInfo.notes.push({
                        id: note.id,
                        title: note.title,
                        currentFolderId: note.folder_id
                    });
                }

                // 统计主题
                if (note.topics) {
                    try {
                        const topics = JSON.parse(note.topics);
                        for (const topic of topics) {
                            if (!topicStats.has(topic)) {
                                topicStats.set(topic, { count: 0, notes: [] });
                            }
                            topicStats.get(topic).count++;
                            topicStats.get(topic).notes.push(note.id);
                        }
                    } catch (error) {
                        // 忽略解析错误
                    }
                }
            }

            // 检查现有文件夹
            const [existingFolders] = await executeWithLog(
                'SELECT id, name FROM folders WHERE user_id = ?',
                [userId]
            );

            const existingFolderNames = new Set(existingFolders.map(f => f.name.toLowerCase()));

            // 生成推荐
            const recommendations = [];

            // 基于分类的推荐
            for (const [category, info] of categoryStats) {
                if (info.count >= 3 && !existingFolderNames.has(category.toLowerCase())) {
                    recommendations.push({
                        type: 'category',
                        name: category,
                        reason: `发现${info.count}篇"${category}"类型的笔记，建议创建专门文件夹`,
                        noteCount: info.count,
                        notes: info.notes,
                        priority: info.count >= 5 ? 'high' : 'medium'
                    });
                }
            }

            // 基于主题的推荐
            const topTopics = Array.from(topicStats.entries())
                .filter(([topic, info]) => info.count >= 4)
                .sort(([,a], [,b]) => b.count - a.count)
                .slice(0, 5);

            for (const [topic, info] of topTopics) {
                if (!existingFolderNames.has(topic.toLowerCase())) {
                    recommendations.push({
                        type: 'topic',
                        name: topic,
                        reason: `主题"${topic}"出现在${info.count}篇笔记中，建议创建主题文件夹`,
                        noteCount: info.count,
                        noteIds: info.notes,
                        priority: info.count >= 8 ? 'high' : 'medium'
                    });
                }
            }

            // 按优先级排序
            recommendations.sort((a, b) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority] || b.noteCount - a.noteCount;
            });

            console.log(`Generated ${recommendations.length} folder recommendations`);

            return {
                recommendations: recommendations.slice(0, 10), // 限制推荐数量
                stats: {
                    totalNotes: analyses.length,
                    categoriesFound: categoryStats.size,
                    topicsFound: topicStats.size,
                    existingFolders: existingFolders.length
                }
            };

        } catch (error) {
            console.error('Error generating folder recommendations:', error);
            throw error;
        }
    }

    /**
     * 获取笔记的关联信息
     * @param {number} userId - 用户ID
     * @param {number} noteId - 笔记ID
     * @returns {Promise<Object>} 关联信息
     */
    async getNoteRelations(userId, noteId) {
        try {
            // 获取作为源的关系
            const [outgoingRelations] = await executeWithLog(
                `SELECT 
                    nr.target_note_id as noteId,
                    nr.relation_type,
                    nr.similarity_score,
                    nr.relation_reason,
                    nr.status,
                    n.title
                 FROM note_relations nr
                 JOIN notes n ON nr.target_note_id = n.id
                 WHERE nr.user_id = ? AND nr.source_note_id = ?
                 ORDER BY nr.similarity_score DESC`,
                [userId, noteId]
            );

            // 获取作为目标的关系
            const [incomingRelations] = await executeWithLog(
                `SELECT 
                    nr.source_note_id as noteId,
                    nr.relation_type,
                    nr.similarity_score,
                    nr.relation_reason,
                    nr.status,
                    n.title
                 FROM note_relations nr
                 JOIN notes n ON nr.source_note_id = n.id
                 WHERE nr.user_id = ? AND nr.target_note_id = ?
                 ORDER BY nr.similarity_score DESC`,
                [userId, noteId]
            );

            return {
                outgoing: outgoingRelations.map(r => ({
                    noteId: r.noteId,
                    title: r.title,
                    relationType: r.relation_type,
                    similarity: Math.round(r.similarity_score * 1000) / 1000,
                    reason: r.relation_reason,
                    status: r.status
                })),
                incoming: incomingRelations.map(r => ({
                    noteId: r.noteId,
                    title: r.title,
                    relationType: r.relation_type,
                    similarity: Math.round(r.similarity_score * 1000) / 1000,
                    reason: r.relation_reason,
                    status: r.status
                }))
            };

        } catch (error) {
            console.error(`Error getting note relations for note ${noteId}:`, error);
            return { outgoing: [], incoming: [] };
        }
    }
}

module.exports = OrganizationService;
