const rateLimit = require('express-rate-limit');

// 登录限制 - 每15分钟最多5次尝试
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: {
    message: '登录尝试次数过多，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // 根据IP和用户名组合限制
  keyGenerator: (req) => {
    return `${req.ip}-${req.body.username || 'unknown'}`;
  },
  // 成功登录后重置计数器
  skipSuccessfulRequests: true,
  // 跳过失败的请求（让应用处理）
  skipFailedRequests: false
});

// 注册限制 - 每小时最多3次注册
const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 最多3次注册
  message: {
    message: '注册尝试次数过多，请1小时后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 密码修改限制 - 每小时最多5次
const passwordChangeLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 5, // 最多5次
  message: {
    message: '密码修改尝试次数过多，请1小时后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// API通用限制 - 每分钟最多100次请求
const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 100, // 最多100次请求
  message: {
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = {
  loginLimiter,
  registerLimiter,
  passwordChangeLimiter,
  apiLimiter
};
