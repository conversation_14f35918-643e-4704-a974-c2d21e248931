// 请求日志中间件
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  console.log(`\n[${timestamp}] ========== 新请求 ==========`);
  console.log(`[REQUEST] ${req.method} ${req.originalUrl}`);
  console.log(`[REQUEST] IP: ${req.ip || req.connection.remoteAddress}`);
  console.log(`[REQUEST] User-Agent: ${req.headers['user-agent']}`);
  
  if (req.headers['authorization']) {
    console.log(`[REQUEST] Authorization: Bearer ${req.headers['authorization'].split(' ')[1]?.substring(0, 20)}...`);
  }
  
  if (Object.keys(req.query).length > 0) {
    console.log(`[REQUEST] Query参数:`, req.query);
  }
  
  if (req.body && Object.keys(req.body).length > 0) {
    // 隐藏敏感信息
    const logBody = { ...req.body };
    if (logBody.password) logBody.password = '***隐藏***';
    console.log(`[REQUEST] Body参数:`, logBody);
  }
  
  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log(`[RESPONSE] 状态码: ${res.statusCode}`);
    console.log(`[RESPONSE] 响应时间: ${duration}ms`);
    console.log(`[${new Date().toISOString()}] ========== 请求结束 ==========\n`);
  });
  
  next();
};

module.exports = {
  requestLogger
};