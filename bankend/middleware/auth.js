const jwt = require('jsonwebtoken');
require('dotenv').config();

// 验证JWT密钥配置
if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
  console.error('SECURITY ERROR: JWT_SECRET must be at least 32 characters long');
  process.exit(1);
}

// 验证JWT token
const authenticateToken = (req, res, next) => {
  console.log(`[AUTH] 验证请求: ${req.method} ${req.originalUrl}`);
  console.log(`[AUTH] 请求头Authorization:`, req.headers['authorization'] ? '存在' : '不存在');

  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    console.log(`[AUTH] 错误: 未提供认证token`);
    return res.status(401).json({ message: '未提供认证token' });
  }

  console.log(`[AUTH] Token前缀: ${token.substring(0, 20)}...`);

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      console.log(`[AUTH] Token验证失败:`, err.message);
      return res.status(403).json({ message: 'token无效或已过期' });
    }

    console.log(`[AUTH] Token验证成功, 用户ID: ${user.userId}, 用户名: ${user.username}`);
    req.user = user;
    next();
  });
};

module.exports = {
  authenticateToken
}; 