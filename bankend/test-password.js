const bcrypt = require('bcrypt');

// 数据库中存储的加密密码
const hashedPassword = '$2b$10$XdJBgD8oDmMvlJ.K6bYE7.QGFTfH5GHjZGfH5XyYxTUHZ9.AGjQAq';

// 常见的测试密码
const testPasswords = [
    'admin',
    'password',
    '123456',
    'admin123',
    'aaaaa',
    'Heihu@110',
    'password123',
    'admin@123',
    '12345678',
    'qwerty'
];

async function testPasswords() {
    console.log('测试数据库中的加密密码对应的真实密码...');
    console.log('加密密码:', hashedPassword);
    console.log('');

    for (const password of testPasswords) {
        try {
            const isMatch = await bcrypt.compare(password, hashedPassword);
            console.log(`密码 "${password}": ${isMatch ? '✅ 匹配' : '❌ 不匹配'}`);

            if (isMatch) {
                console.log(`\n🎉 找到匹配的密码: "${password}"`);
                break;
            }
        } catch (error) {
            console.log(`密码 "${password}": ❌ 验证出错 -`, error.message);
        }
    }
}

testPasswords().catch(console.error);