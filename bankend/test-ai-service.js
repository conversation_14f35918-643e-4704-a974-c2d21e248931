const aiService = require('./services/aiService');
require('dotenv').config();

async function testAIService() {
    try {
        console.log('Testing AI Service...');
        
        console.log('AI Service available:', aiService.isAvailable());
        console.log('Model info:', aiService.getModelInfo());
        
        if (!aiService.isAvailable()) {
            console.log('AI Service not available, exiting test');
            return;
        }
        
        // 测试直接内容生成
        console.log('\n--- Testing direct content generation ---');
        try {
            const result = await aiService.generateContent('Hello, please respond in Chinese and tell me about AI.');
            console.log('Generated content:', result.response.text());
        } catch (error) {
            console.error('Content generation error:', error.message);
        }
        
        // 测试聊天功能
        console.log('\n--- Testing chat functionality ---');
        try {
            const chat = aiService.createChat([]);
            const chatResult = await chat.sendMessage('What is the capital of China?');
            console.log('Chat response:', chatResult.response.text());
        } catch (error) {
            console.error('Chat error:', error.message);
        }
        
        // 测试embedding功能
        console.log('\n--- Testing embedding functionality ---');
        try {
            const embedding = await aiService.generateEmbedding('This is a test sentence for embedding.');
            console.log('Embedding generated, length:', embedding.length);
            console.log('First 5 values:', embedding.slice(0, 5));
        } catch (error) {
            console.error('Embedding error:', error.message);
        }
        
        // 测试内容分析
        console.log('\n--- Testing content analysis ---');
        try {
            const analysis = await aiService.analyzeContent('Analyze this text: "AI is transforming the world"');
            console.log('Analysis result:', analysis);
        } catch (error) {
            console.error('Analysis error:', error.message);
        }
        
        console.log('\n--- AI Service test completed ---');
        
    } catch (error) {
        console.error('Test error:', error);
    }
}

testAIService();
