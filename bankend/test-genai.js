const { GoogleGenerativeAI } = require('@google/generative-ai');
const { HttpsProxyAgent } = require('https-proxy-agent');
require('dotenv').config();

// 配置代理
const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
const agent = new HttpsProxyAgent(proxyUrl);

// 配置全局fetch使用代理
const originalFetch = global.fetch;
global.fetch = (url, options = {}) => {
    return originalFetch(url, {
        ...options,
        agent: agent
    });
};

async function testGenAI() {
    try {
        console.log('Testing Google GenAI API...');

        if (!process.env.GEMINI_API_KEY) {
            console.error('GEMINI_API_KEY not found in environment');
            return;
        }

        console.log('API Key found:', process.env.GEMINI_API_KEY.substring(0, 10) + '...');

        // 初始化GenAI
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        console.log('GenAI initialized successfully');

        // 测试获取模型
        try {
            // 使用可用的gemini-1.5-flash模型
            const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
            console.log('Model obtained successfully');

            // 测试生成内容
            console.log('Generating content...');
            const result = await model.generateContent('Hello, how are you?');
            const response = result.response;
            const text = response.text();
            console.log('Generated text:', text);

        } catch (modelError) {
            console.error('Model error:', modelError.message);
            console.error('Full error:', modelError);
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
        console.error('Full error:', error);
    }
}

testGenAI();
