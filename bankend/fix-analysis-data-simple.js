const { executeWithLog } = require('./db');
require('dotenv').config();

async function fixAnalysisDataSimple() {
    try {
        console.log('开始修复分析数据...');
        
        // 获取所有分析记录
        const [analyses] = await executeWithLog(
            'SELECT id, note_id, keywords, topics FROM note_analysis',
            []
        );
        
        console.log(`找到 ${analyses.length} 条分析记录`);
        
        let fixedCount = 0;
        
        for (const analysis of analyses) {
            console.log(`\n处理记录 ${analysis.id} (笔记 ${analysis.note_id}):`);
            console.log(`原始关键词: ${analysis.keywords}`);
            console.log(`原始主题: ${analysis.topics}`);
            
            let newKeywords = analysis.keywords;
            let newTopics = analysis.topics;
            let needsUpdate = false;
            
            // 修复关键词
            if (analysis.keywords && typeof analysis.keywords === 'string') {
                try {
                    JSON.parse(analysis.keywords);
                    console.log('关键词JSON格式正确');
                } catch (error) {
                    console.log('修复关键词格式...');
                    // 简单分割并转换为JSON数组
                    const keywordItems = analysis.keywords
                        .split(',')
                        .map(item => item.trim())
                        .filter(item => item.length > 0);
                    
                    newKeywords = JSON.stringify(keywordItems);
                    needsUpdate = true;
                    console.log(`新关键词: ${newKeywords}`);
                }
            } else {
                newKeywords = '[]';
                needsUpdate = true;
                console.log('设置空关键词数组');
            }
            
            // 修复主题
            if (analysis.topics && typeof analysis.topics === 'string') {
                try {
                    JSON.parse(analysis.topics);
                    console.log('主题JSON格式正确');
                } catch (error) {
                    console.log('修复主题格式...');
                    // 简单分割并转换为JSON数组
                    const topicItems = analysis.topics
                        .split(',')
                        .map(item => item.trim())
                        .filter(item => item.length > 0);
                    
                    newTopics = JSON.stringify(topicItems.length > 0 ? topicItems : ['未分类']);
                    needsUpdate = true;
                    console.log(`新主题: ${newTopics}`);
                }
            } else {
                newTopics = '["未分类"]';
                needsUpdate = true;
                console.log('设置默认主题');
            }
            
            // 如果需要更新，执行更新
            if (needsUpdate) {
                try {
                    await executeWithLog(
                        'UPDATE note_analysis SET keywords = ?, topics = ? WHERE id = ?',
                        [newKeywords, newTopics, analysis.id]
                    );
                    
                    console.log(`✅ 记录 ${analysis.id} 已修复`);
                    fixedCount++;
                } catch (updateError) {
                    console.error(`❌ 更新记录 ${analysis.id} 失败:`, updateError);
                }
            } else {
                console.log('无需修复');
            }
        }
        
        console.log(`\n修复完成！共修复了 ${fixedCount} 条记录`);
        
        // 验证修复结果
        console.log('\n验证修复结果...');
        const [verifyAnalyses] = await executeWithLog(
            'SELECT id, note_id, keywords, topics FROM note_analysis LIMIT 3',
            []
        );
        
        for (const analysis of verifyAnalyses) {
            try {
                const keywords = JSON.parse(analysis.keywords || '[]');
                const topics = JSON.parse(analysis.topics || '[]');
                console.log(`✅ 记录 ${analysis.id}: 关键词 ${keywords.length} 个, 主题 ${topics.length} 个`);
                console.log(`   关键词: ${keywords.join(', ')}`);
                console.log(`   主题: ${topics.join(', ')}`);
            } catch (error) {
                console.log(`❌ 记录 ${analysis.id}: JSON格式仍有问题 - ${error.message}`);
            }
        }
        
    } catch (error) {
        console.error('修复过程中出错:', error);
    }
}

// 运行修复脚本
fixAnalysisDataSimple();
