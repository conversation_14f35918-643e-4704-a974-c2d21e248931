const express = require('express');
const cors = require('cors');
require('dotenv').config();
const { testConnection } = require('./db');
const { requestLogger } = require('./middleware/logger');
const { securityHeaders, hideServerInfo } = require('./middleware/security');
const { apiLimiter } = require('./middleware/rateLimiter');

// 导入路由
const authRoutes = require('./routes/auth');
const folderRoutes = require('./routes/folders');
const noteRoutes = require('./routes/notes');
const aiRoutes = require('./routes/ai');
const knowledgeRoutes = require('./routes/knowledge');

// 初始化应用
const app = express();
const PORT = process.env.PORT || 3001;

// 中间件: 配置 CORS，限制允许的源
const allowedOrigins = [
  'http://localhost:5173',
  'http://localhost:5174',
  'http://localhost:3000',
  'http://127.0.0.1:5173',
  'http://127.0.0.1:5174',
  'http://127.0.0.1:3000',
  process.env.CORS_ORIGIN || 'http://localhost:5173'
].filter(Boolean);

const corsOptions = {
  origin: (origin, callback) => {
    // 允许没有origin的请求（如移动应用或Postman）
    if (!origin) return callback(null, true);

    // 临时允许file://协议用于测试
    if (origin && origin.startsWith('file://')) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  optionsSuccessStatus: 200 // 支持旧版浏览器
};
// 安全中间件
app.use(hideServerInfo);
app.use(securityHeaders);

// 全局API限制
app.use('/api/', apiLimiter);

app.use(cors(corsOptions));
// 对所有预检请求应用 CORS
app.options('*', cors(corsOptions));

// 限制请求体大小，防止DoS攻击
app.use(express.json({ limit: '10mb' })); // 减少到10MB
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 添加请求日志中间件
app.use(requestLogger);

// 测试数据库连接
testConnection();

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/folders', folderRoutes);
app.use('/api/notes', noteRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/knowledge', knowledgeRoutes);

// 根路由
app.get('/', (req, res) => {
  res.json({ message: '欢迎使用笔记应用API' });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: '服务器错误', error: process.env.NODE_ENV === 'development' ? err.message : undefined });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
}); 