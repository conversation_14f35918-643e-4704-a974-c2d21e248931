-- 知识库数据库结构设计
-- 用于支持AI搜索、分析和整理功能

-- 1. 笔记向量存储表
CREATE TABLE note_vectors (
    id INT PRIMARY KEY AUTO_INCREMENT,
    note_id INT NOT NULL,
    user_id INT NOT NULL,
    content_hash VARCHAR(64) NOT NULL COMMENT '内容哈希，用于检测内容变化',
    embedding_vector JSON NOT NULL COMMENT '内容向量（1536维）',
    chunk_index INT DEFAULT 0 COMMENT '分块索引，长文本分块存储',
    chunk_text TEXT NOT NULL COMMENT '分块文本内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_note_vectors_note_id (note_id),
    INDEX idx_note_vectors_user_id (user_id),
    INDEX idx_note_vectors_hash (content_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 笔记分析结果表
CREATE TABLE note_analysis (
    id INT PRIMARY KEY AUTO_INCREMENT,
    note_id INT NOT NULL,
    user_id INT NOT NULL,
    
    -- 内容分析结果
    summary TEXT COMMENT 'AI生成的摘要',
    keywords JSON COMMENT '关键词列表',
    topics JSON COMMENT '主题标签',
    sentiment_score DECIMAL(3,2) COMMENT '情感分数 (-1到1)',
    complexity_score DECIMAL(3,2) COMMENT '复杂度分数 (0到1)',
    
    -- 内容特征
    word_count INT DEFAULT 0,
    char_count INT DEFAULT 0,
    language VARCHAR(10) DEFAULT 'zh-CN',
    
    -- 分类信息
    category VARCHAR(100) COMMENT 'AI推荐分类',
    confidence_score DECIMAL(3,2) COMMENT '分类置信度',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_note_analysis (note_id),
    INDEX idx_analysis_user_id (user_id),
    INDEX idx_analysis_category (category),
    INDEX idx_analysis_topics (topics(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 笔记关联关系表
CREATE TABLE note_relations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    source_note_id INT NOT NULL,
    target_note_id INT NOT NULL,
    
    -- 关联类型和强度
    relation_type ENUM('similar', 'reference', 'follow_up', 'contradiction', 'supplement') NOT NULL,
    similarity_score DECIMAL(4,3) NOT NULL COMMENT '相似度分数 (0到1)',
    
    -- AI分析的关联原因
    relation_reason TEXT COMMENT 'AI分析的关联原因',
    
    -- 状态管理
    status ENUM('auto', 'confirmed', 'rejected') DEFAULT 'auto',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (source_note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (target_note_id) REFERENCES notes(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_relation (source_note_id, target_note_id),
    INDEX idx_relations_user_id (user_id),
    INDEX idx_relations_source (source_note_id),
    INDEX idx_relations_target (target_note_id),
    INDEX idx_relations_type (relation_type),
    INDEX idx_relations_similarity (similarity_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 知识库搜索历史表
CREATE TABLE search_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    query_text TEXT NOT NULL,
    query_vector JSON COMMENT '查询向量',
    
    -- 搜索结果
    result_count INT DEFAULT 0,
    result_note_ids JSON COMMENT '搜索到的笔记ID列表',
    
    -- 搜索参数
    search_type ENUM('semantic', 'keyword', 'hybrid') DEFAULT 'semantic',
    similarity_threshold DECIMAL(3,2) DEFAULT 0.7,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_search_user_id (user_id),
    INDEX idx_search_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 智能标签表
CREATE TABLE smart_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tag_name VARCHAR(100) NOT NULL,
    tag_type ENUM('auto', 'manual', 'system') DEFAULT 'auto',
    
    -- 标签统计
    usage_count INT DEFAULT 0,
    
    -- 标签向量（用于标签推荐）
    tag_vector JSON COMMENT '标签向量',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_tag (user_id, tag_name),
    INDEX idx_tags_type (tag_type),
    INDEX idx_tags_usage (usage_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. 笔记标签关联表
CREATE TABLE note_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    note_id INT NOT NULL,
    tag_id INT NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 1.0 COMMENT '标签置信度',
    assigned_by ENUM('ai', 'user') DEFAULT 'ai',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES smart_tags(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_note_tag (note_id, tag_id),
    INDEX idx_note_tags_confidence (confidence_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. 知识库配置表
CREATE TABLE knowledge_base_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    
    -- 向量化配置
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-3-small',
    chunk_size INT DEFAULT 1000,
    chunk_overlap INT DEFAULT 200,
    
    -- 搜索配置
    default_similarity_threshold DECIMAL(3,2) DEFAULT 0.7,
    max_search_results INT DEFAULT 20,
    
    -- 分析配置
    auto_analysis_enabled BOOLEAN DEFAULT TRUE,
    auto_tagging_enabled BOOLEAN DEFAULT TRUE,
    auto_relation_enabled BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_config (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
