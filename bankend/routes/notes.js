const express = require('express');
const router = express.Router();
const { executeWithLog } = require('../db');
const { authenticateToken } = require('../middleware/auth');
const { validateNote } = require('../middleware/validation');

// 获取用户所有笔记
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const folderId = req.query.folderId;

    let query = 'SELECT id, folder_id as folderId, title, content, created_at as createdAt, updated_at as updatedAt FROM notes WHERE user_id = ?';
    let params = [userId];

    if (folderId) {
      query += ' AND folder_id = ?';
      params.push(folderId);
    }

    query += ' ORDER BY updated_at DESC';

    const [notes] = await executeWithLog(query, params);

    // 计算每个笔记的大小
    const notesWithSize = notes.map(note => {
      // 简单计算内容大小（字节）
      const size = Buffer.byteLength(note.content || '', 'utf8');
      let sizeStr = '';

      if (size < 1024) {
        sizeStr = `${size} B`;
      } else if (size < 1024 * 1024) {
        sizeStr = `${(size / 1024).toFixed(1)} KB`;
      } else {
        sizeStr = `${(size / (1024 * 1024)).toFixed(1)} MB`;
      }

      return {
        ...note,
        size: sizeStr
      };
    });

    res.json(notesWithSize);
  } catch (error) {
    console.error('获取笔记错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 获取单个笔记
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const noteId = req.params.id;
    const userId = req.user.userId;

    const [notes] = await executeWithLog(
      'SELECT id, folder_id as folderId, title, content, created_at as createdAt, updated_at as updatedAt FROM notes WHERE id = ? AND user_id = ?',
      [noteId, userId]
    );

    if (notes.length === 0) {
      return res.status(404).json({ message: '笔记不存在或无权访问' });
    }

    // 计算笔记大小
    const note = notes[0];
    const size = Buffer.byteLength(note.content || '', 'utf8');
    let sizeStr = '';

    if (size < 1024) {
      sizeStr = `${size} B`;
    } else if (size < 1024 * 1024) {
      sizeStr = `${(size / 1024).toFixed(1)} KB`;
    } else {
      sizeStr = `${(size / (1024 * 1024)).toFixed(1)} MB`;
    }

    res.json({
      ...note,
      size: sizeStr
    });
  } catch (error) {
    console.error('获取笔记错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 创建新笔记
router.post('/', authenticateToken, validateNote, async (req, res) => {
  try {
    const { title, content, folderId } = req.body;
    const userId = req.user.userId;

    // 验证文件夹归属（如果指定了文件夹）
    if (folderId != null) {
      const [folderCheck] = await executeWithLog(
        'SELECT * FROM folders WHERE id = ? AND user_id = ?',
        [folderId, userId]
      );

      if (folderCheck.length === 0) {
        return res.status(404).json({ message: '文件夹不存在或无权访问' });
      }
    }

    // 统一默认值
    const folderParam = folderId != null ? folderId : null;
    const titleParam = typeof title === 'string' && title.trim() ? title.trim() : '无标题笔记';
    const contentParam = typeof content === 'string' ? content : '';

    const [result] = await executeWithLog(
      'INSERT INTO notes (user_id, folder_id, title, content) VALUES (?, ?, ?, ?)',
      [userId, folderParam, titleParam, contentParam]
    );

    const [newNote] = await executeWithLog(
      'SELECT id, folder_id as folderId, title, content, created_at as createdAt, updated_at as updatedAt FROM notes WHERE id = ?',
      [result.insertId]
    );

    // 计算笔记大小
    const note = newNote[0];
    const size = Buffer.byteLength(note.content || '', 'utf8');
    let sizeStr = '';

    if (size < 1024) {
      sizeStr = `${size} B`;
    } else if (size < 1024 * 1024) {
      sizeStr = `${(size / 1024).toFixed(1)} KB`;
    } else {
      sizeStr = `${(size / (1024 * 1024)).toFixed(1)} MB`;
    }

    res.status(201).json({
      ...note,
      size: sizeStr
    });
  } catch (error) {
    console.error('创建笔记错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 更新笔记
router.put('/:id', authenticateToken, validateNote, async (req, res) => {
  try {
    const { title, content, folderId } = req.body;
    const noteId = req.params.id;
    const userId = req.user.userId;

    // 验证笔记归属
    const [noteCheck] = await executeWithLog(
      'SELECT * FROM notes WHERE id = ? AND user_id = ?',
      [noteId, userId]
    );

    if (noteCheck.length === 0) {
      return res.status(404).json({ message: '笔记不存在或无权访问' });
    }

    // 验证文件夹归属（如果指定了文件夹）
    if (folderId !== undefined && folderId !== null) {
      const [folderCheck] = await executeWithLog(
        'SELECT * FROM folders WHERE id = ? AND user_id = ?',
        [folderId, userId]
      );

      if (folderCheck.length === 0) {
        return res.status(404).json({ message: '文件夹不存在或无权访问' });
      }
    }

    // 取出现有笔记，用于默认值
    const existing = noteCheck[0];
    const updatedTitle = typeof title === 'string' ? title : existing.title;
    const updatedContent = typeof content === 'string' ? content : existing.content;
    const updatedFolderId = folderId !== undefined ? folderId : existing.folder_id;

    // 更新笔记
    await executeWithLog(
      'UPDATE notes SET title = ?, content = ?, folder_id = ? WHERE id = ?',
      [updatedTitle, updatedContent, updatedFolderId || null, noteId]
    );

    const [updatedNote] = await executeWithLog(
      'SELECT id, folder_id as folderId, title, content, created_at as createdAt, updated_at as updatedAt FROM notes WHERE id = ?',
      [noteId]
    );

    // 计算笔记大小
    const note = updatedNote[0];
    const size = Buffer.byteLength(note.content || '', 'utf8');
    let sizeStr = '';

    if (size < 1024) {
      sizeStr = `${size} B`;
    } else if (size < 1024 * 1024) {
      sizeStr = `${(size / 1024).toFixed(1)} KB`;
    } else {
      sizeStr = `${(size / (1024 * 1024)).toFixed(1)} MB`;
    }

    res.json({
      ...note,
      size: sizeStr
    });
  } catch (error) {
    console.error('更新笔记错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 删除笔记
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const noteId = req.params.id;
    const userId = req.user.userId;

    // 验证笔记归属
    const [noteCheck] = await executeWithLog(
      'SELECT * FROM notes WHERE id = ? AND user_id = ?',
      [noteId, userId]
    );

    if (noteCheck.length === 0) {
      return res.status(404).json({ message: '笔记不存在或无权访问' });
    }

    // 删除笔记
    await executeWithLog('DELETE FROM notes WHERE id = ?', [noteId]);

    res.json({ message: '笔记已删除' });
  } catch (error) {
    console.error('删除笔记错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

module.exports = router; 