const express = require('express');
const router = express.Router();
const { executeWithLog } = require('../db');
const { authenticateToken } = require('../middleware/auth');
const { validateFolder } = require('../middleware/validation');

// 获取用户所有文件夹
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const [folders] = await executeWithLog(
      'SELECT id, name, parent_folder_id as parentId, created_at as createdAt, updated_at as updatedAt FROM folders WHERE user_id = ?',
      [userId]
    );

    res.json(folders);
  } catch (error) {
    console.error('获取文件夹错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 创建新文件夹
router.post('/', authenticateToken, validateFolder, async (req, res) => {
  try {
    const { name, parentId } = req.body;
    const userId = req.user.userId;

    if (!name) {
      return res.status(400).json({ message: '文件夹名称不能为空' });
    }

    // 统一默认值
    const parentParam = parentId != null ? parentId : null;

    const [result] = await executeWithLog(
      'INSERT INTO folders (user_id, name, parent_folder_id) VALUES (?, ?, ?)',
      [userId, name, parentParam]
    );

    const [newFolder] = await executeWithLog(
      'SELECT id, name, parent_folder_id as parentId, created_at as createdAt, updated_at as updatedAt FROM folders WHERE id = ?',
      [result.insertId]
    );

    res.status(201).json(newFolder[0]);
  } catch (error) {
    console.error('创建文件夹错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 更新文件夹
router.put('/:id', authenticateToken, validateFolder, async (req, res) => {
  try {
    const { name, parentId } = req.body;
    const folderId = req.params.id;
    const userId = req.user.userId;

    // 验证文件夹归属
    const [folderCheck] = await executeWithLog(
      'SELECT * FROM folders WHERE id = ? AND user_id = ?',
      [folderId, userId]
    );

    if (folderCheck.length === 0) {
      return res.status(404).json({ message: '文件夹不存在或无权访问' });
    }

    // 更新文件夹
    await executeWithLog(
      'UPDATE folders SET name = ?, parent_folder_id = ? WHERE id = ?',
      [name, parentId || null, folderId]
    );

    const [updatedFolder] = await executeWithLog(
      'SELECT id, name, parent_folder_id as parentId, created_at as createdAt, updated_at as updatedAt FROM folders WHERE id = ?',
      [folderId]
    );

    res.json(updatedFolder[0]);
  } catch (error) {
    console.error('更新文件夹错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 删除文件夹
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const folderId = req.params.id;
    const userId = req.user.userId;

    // 验证文件夹归属
    const [folderCheck] = await executeWithLog(
      'SELECT * FROM folders WHERE id = ? AND user_id = ?',
      [folderId, userId]
    );

    if (folderCheck.length === 0) {
      return res.status(404).json({ message: '文件夹不存在或无权访问' });
    }

    // 删除文件夹 (外键约束会自动处理子文件夹和笔记)
    await executeWithLog('DELETE FROM folders WHERE id = ?', [folderId]);

    res.json({ message: '文件夹已删除' });
  } catch (error) {
    console.error('删除文件夹错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

module.exports = router; 