const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { executeWithLog } = require('../db');
const { authenticateToken } = require('../middleware/auth');
const { loginLimiter, registerLimiter, passwordChangeLimiter } = require('../middleware/rateLimiter');

// 登录
router.post('/login', loginLimiter, async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log(`[LOGIN] 登录尝试 - 用户名: ${username}`);

    // 查询用户
    const [rows] = await executeWithLog(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );

    if (rows.length === 0) {
      console.log(`[LOGIN] 用户不存在: ${username}`);
      return res.status(401).json({ message: '用户名或密码错误' });
    }

    const user = rows[0];
    console.log(`[LOGIN] 找到用户: ${user.username}, ID: ${user.id}`);

    // 验证密码 - 只使用bcrypt，不允许明文密码
    let isPasswordValid = false;

    try {
      isPasswordValid = await bcrypt.compare(password, user.password);
      console.log(`[LOGIN] 密码验证结果: ${isPasswordValid}`);
    } catch (bcryptError) {
      console.log(`[LOGIN] 密码验证失败:`, bcryptError.message);
      // 不再支持明文密码比较，确保安全性
      isPasswordValid = false;
    }

    if (!isPasswordValid) {
      console.log(`[LOGIN] 密码验证失败 - 用户: ${username}`);
      return res.status(401).json({ message: '用户名或密码错误' });
    }

    console.log(`[LOGIN] 密码验证成功 - 用户: ${username}`);

    // 创建JWT
    const token = jwt.sign(
      { userId: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log(`[LOGIN] JWT创建成功 - 用户: ${username}`);

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 密码强度验证函数
const validatePassword = (password) => {
  if (!password || password.length < 8) {
    return { valid: false, message: '密码长度至少为8位' };
  }
  if (!/(?=.*[a-z])/.test(password)) {
    return { valid: false, message: '密码必须包含至少一个小写字母' };
  }
  if (!/(?=.*[A-Z])/.test(password)) {
    return { valid: false, message: '密码必须包含至少一个大写字母' };
  }
  if (!/(?=.*\d)/.test(password)) {
    return { valid: false, message: '密码必须包含至少一个数字' };
  }
  if (!/(?=.*[@$!%*?&])/.test(password)) {
    return { valid: false, message: '密码必须包含至少一个特殊字符(@$!%*?&)' };
  }
  return { valid: true };
};

// 注册
router.post('/register', registerLimiter, async (req, res) => {
  try {
    const { username, password } = req.body;

    // 输入验证
    if (!username || !password) {
      return res.status(400).json({ message: '用户名和密码不能为空' });
    }

    // 用户名验证
    if (username.length < 3 || username.length > 50) {
      return res.status(400).json({ message: '用户名长度必须在3-50个字符之间' });
    }

    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
      return res.status(400).json({ message: '用户名只能包含字母、数字和下划线' });
    }

    // 密码强度验证
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      return res.status(400).json({ message: passwordValidation.message });
    }

    // 检查用户名是否已存在
    const [existingUsers] = await executeWithLog(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ message: '用户名已存在' });
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    // 创建新用户
    const [result] = await executeWithLog(
      'INSERT INTO users (username, password) VALUES (?, ?)',
      [username, hashedPassword]
    );

    // 创建JWT
    const token = jwt.sign(
      { userId: result.insertId, username },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.status(201).json({
      message: '注册成功',
      token,
      user: {
        id: result.insertId,
        username
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 验证token
router.get('/verify', async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: '未提供token' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 查询用户
    const [rows] = await executeWithLog(
      'SELECT id, username FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: '用户不存在' });
    }

    res.json({
      user: {
        id: rows[0].id,
        username: rows[0].username
      }
    });
  } catch (error) {
    console.error('Token验证错误:', error);
    res.status(401).json({ message: 'Token无效或已过期' });
  }
});

// 验证当前密码
router.post('/verify-current-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword } = req.body;
    const userId = req.user.userId;

    console.log(`[VERIFY_CURRENT_PASSWORD] 用户 ${req.user.username} 请求验证当前密码`);

    // 验证输入
    if (!currentPassword) {
      return res.status(400).json({ message: '请提供当前密码' });
    }

    // 查询用户当前密码
    const [rows] = await executeWithLog(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: '用户不存在' });
    }

    const user = rows[0];

    // 验证当前密码 - 只使用bcrypt
    let isCurrentPasswordValid = false;
    try {
      isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      console.log(`[VERIFY_CURRENT_PASSWORD] 密码验证结果: ${isCurrentPasswordValid}`);
    } catch (bcryptError) {
      console.log(`[VERIFY_CURRENT_PASSWORD] 密码验证失败:`, bcryptError.message);
      // 不再支持明文密码比较
      isCurrentPasswordValid = false;
    }

    if (!isCurrentPasswordValid) {
      console.log(`[VERIFY_CURRENT_PASSWORD] 当前密码验证失败 - 用户: ${req.user.username}`);
      return res.status(401).json({ message: '当前密码错误' });
    }

    console.log(`[VERIFY_CURRENT_PASSWORD] 当前密码验证成功 - 用户: ${req.user.username}`);
    res.json({ message: '当前密码验证成功' });
  } catch (error) {
    console.error('验证当前密码错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 修改密码
router.put('/change-password', passwordChangeLimiter, authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.userId;

    console.log(`[CHANGE_PASSWORD] 用户 ${req.user.username} 请求修改密码`);

    // 验证输入
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: '请提供当前密码和新密码' });
    }

    // 新密码强度验证
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.valid) {
      return res.status(400).json({ message: passwordValidation.message });
    }

    // 查询用户当前密码
    const [rows] = await executeWithLog(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: '用户不存在' });
    }

    const user = rows[0];

    // 验证当前密码 - 只使用bcrypt
    let isCurrentPasswordValid = false;
    try {
      isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      console.log(`[CHANGE_PASSWORD] 当前密码验证结果: ${isCurrentPasswordValid}`);
    } catch (bcryptError) {
      console.log(`[CHANGE_PASSWORD] 密码验证失败:`, bcryptError.message);
      // 不再支持明文密码比较
      isCurrentPasswordValid = false;
    }

    if (!isCurrentPasswordValid) {
      console.log(`[CHANGE_PASSWORD] 当前密码验证失败 - 用户: ${req.user.username}`);
      return res.status(401).json({ message: '当前密码错误' });
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // 更新密码
    await executeWithLog(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedNewPassword, userId]
    );

    console.log(`[CHANGE_PASSWORD] 密码修改成功 - 用户: ${req.user.username}`);

    res.json({ message: '密码修改成功' });
  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

module.exports = router;