const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { apiLimiter } = require('../middleware/rateLimiter');
const { validateInput } = require('../middleware/validation');
const aiService = require('../services/aiService');

// 验证AI消息输入
const validateAIMessage = (req, res, next) => {
  const { message } = req.body;
  
  if (!message || typeof message !== 'string') {
    return res.status(400).json({ message: '消息内容不能为空' });
  }
  
  if (message.length > 2000) {
    return res.status(400).json({ message: '消息长度不能超过2000个字符' });
  }
  
  // 基本内容过滤
  const sanitized = message.trim();
  if (!sanitized) {
    return res.status(400).json({ message: '消息内容不能为空' });
  }
  
  req.body.message = sanitized;
  next();
};

// AI聊天接口
router.post('/chat', apiLimiter, authenticateToken, validateAIMessage, async (req, res) => {
  if (!aiService.isAvailable()) {
    return res.status(503).json({ message: 'AI服务暂时不可用' });
  }

  try {
    const { message, history = [] } = req.body;
    console.log(`[AI] 用户 ${req.user.username} 发送消息`);

    const chat = aiService.createChat(history.slice(-10));
    const result = await chat.sendMessage(message);

    res.json({
      response: result.response.text(),
      searchMetadata: result.response.groundingMetadata,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('AI chat error:', error);
    res.status(500).json({ message: 'AI服务出现错误，请稍后重试' });
  }
});

// AI流式聊天接口
router.post('/chat/stream', apiLimiter, authenticateToken, validateAIMessage, async (req, res) => {
  if (!aiService.isAvailable()) {
    return res.status(503).json({ message: 'AI服务暂时不可用' });
  }

  try {
    const { message, history = [] } = req.body;
    console.log(`[AI] 用户 ${req.user.username} 发送流式消息`);

    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': req.headers.origin || '*',
      'Access-Control-Allow-Credentials': 'true'
    });

    const chat = aiService.createChat(history.slice(-10));

    // 使用新的流式API
    await aiService.sendMessageStream(
      chat,
      message,
      (chunkText) => {
        if (chunkText) {
          res.write(`data: ${JSON.stringify({ text: chunkText })}\n\n`);
        }
      },
      (searchInfo) => {
        if (searchInfo) {
          res.write(`data: ${JSON.stringify({ searchInfo })}\n\n`);
        }
      }
    );

    res.write(`data: ${JSON.stringify({ done: true })}\n\n`);
    res.end();

  } catch (error) {
    console.error('AI stream error:', error);
    res.write(`data: ${JSON.stringify({ error: 'AI服务出现错误' })}\n\n`);
    res.end();
  }
});

// 检查AI服务状态
router.get('/status', authenticateToken, (req, res) => {
  res.json(aiService.getModelInfo());
});

module.exports = router;

