{"name": "notes-backend", "version": "1.0.0", "description": "Notes application backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "https-proxy-agent": "^7.0.6", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.1", "node-fetch": "^2.7.0", "validator": "^13.15.15"}, "devDependencies": {"nodemon": "^3.0.1"}}