<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平滑切换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
        }
        
        .note-list {
            padding: 20px;
        }
        
        .note-item {
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background: white;
            cursor: pointer;
            /* 移除transition避免闪动 */
        }
        
        .note-item.active {
            background: #eff6ff;
            border-color: #3b82f6;
        }
        
        .note-item h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .note-item p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
        }
        
        .editor {
            flex: 1;
            background: white;
            padding: 40px;
            overflow-y: auto;
        }
        
        .editor-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            outline: none;
        }
        
        .editor-content {
            min-height: 400px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            outline: none;
            line-height: 1.6;
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1f2937;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
        }
        
        .flash-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ef4444;
            color: white;
            padding: 20px 40px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            z-index: 2000;
            display: none;
        }
    </style>
</head>
<body>
    <div class="status" id="status">
        平滑切换测试 - 点击不同笔记测试是否有闪动
    </div>
    
    <div class="flash-indicator" id="flashIndicator">
        检测到页面闪动！
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="note-list">
                <div class="note-item active" data-note-id="1">
                    <h3>我的自定义标题 1</h3>
                    <p>这是第一个笔记的预览内容...</p>
                </div>
                
                <div class="note-item" data-note-id="2">
                    <h3>工作计划</h3>
                    <p>今天需要完成的工作任务列表...</p>
                </div>
                
                <div class="note-item" data-note-id="3">
                    <h3>学习笔记</h3>
                    <p>JavaScript 高级特性学习记录...</p>
                </div>
                
                <div class="note-item" data-note-id="4">
                    <h3>项目想法</h3>
                    <p>新项目的创意和实现思路...</p>
                </div>
                
                <div class="note-item" data-note-id="5">
                    <h3>会议记录</h3>
                    <p>团队会议的重要讨论点和决策...</p>
                </div>
            </div>
        </div>
        
        <div class="editor">
            <input type="text" class="editor-title" id="editorTitle" value="我的自定义标题 1" placeholder="无标题">
            <div class="editor-content" id="editorContent" contenteditable="true">
                <h1>我的自定义标题 1</h1>
                <p>这是第一个笔记的详细内容。你可以在这里编辑笔记内容。</p>
                <p>测试要点：</p>
                <ul>
                    <li>点击左侧不同的笔记项目</li>
                    <li>观察是否有页面闪动</li>
                    <li>标题是否保持你的修改</li>
                    <li>切换应该是平滑的，没有重新渲染的感觉</li>
                </ul>
                <p>如果实现正确，切换笔记时应该只更新必要的内容，而不是重新渲染整个页面。</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟笔记数据
        const notes = {
            '1': {
                title: '我的自定义标题 1',
                content: `
                    <h1>我的自定义标题 1</h1>
                    <p>这是第一个笔记的详细内容。你可以在这里编辑笔记内容。</p>
                    <p>测试要点：</p>
                    <ul>
                        <li>点击左侧不同的笔记项目</li>
                        <li>观察是否有页面闪动</li>
                        <li>标题是否保持你的修改</li>
                        <li>切换应该是平滑的，没有重新渲染的感觉</li>
                    </ul>
                    <p>如果实现正确，切换笔记时应该只更新必要的内容，而不是重新渲染整个页面。</p>
                `
            },
            '2': {
                title: '工作计划',
                content: `
                    <h1>工作计划</h1>
                    <h2>今日任务</h2>
                    <ul>
                        <li>完成项目文档</li>
                        <li>代码审查</li>
                        <li>团队会议</li>
                    </ul>
                    <h2>本周目标</h2>
                    <p>完成新功能的开发和测试。</p>
                `
            },
            '3': {
                title: '学习笔记',
                content: `
                    <h1>JavaScript 高级特性</h1>
                    <h2>闭包</h2>
                    <p>闭包是指有权访问另一个函数作用域中变量的函数。</p>
                    <h2>原型链</h2>
                    <p>JavaScript 中的继承是通过原型链实现的。</p>
                `
            },
            '4': {
                title: '项目想法',
                content: `
                    <h1>新项目创意</h1>
                    <h2>智能笔记应用</h2>
                    <p>一个具有AI功能的笔记应用，可以：</p>
                    <ul>
                        <li>自动分类笔记</li>
                        <li>智能搜索</li>
                        <li>内容推荐</li>
                    </ul>
                `
            },
            '5': {
                title: '会议记录',
                content: `
                    <h1>团队会议记录</h1>
                    <h2>参会人员</h2>
                    <p>张三、李四、王五</p>
                    <h2>讨论要点</h2>
                    <ul>
                        <li>项目进度回顾</li>
                        <li>下阶段计划</li>
                        <li>资源分配</li>
                    </ul>
                `
            }
        };
        
        let currentNoteId = '1';
        let switchCount = 0;
        
        // 检测页面闪动的观察器
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.target === document.body) {
                    // 如果整个body被重新渲染，显示闪动警告
                    showFlashWarning();
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        function showFlashWarning() {
            const indicator = document.getElementById('flashIndicator');
            indicator.style.display = 'block';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 2000);
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 平滑切换笔记的函数（模拟修复后的行为）
        function switchNote(noteId) {
            if (noteId === currentNoteId) return;
            
            switchCount++;
            
            // 保存当前笔记的标题修改
            const titleInput = document.getElementById('editorTitle');
            if (currentNoteId && titleInput) {
                notes[currentNoteId].title = titleInput.value;
            }
            
            // 更新选中状态（只操作CSS类，不重新渲染）
            document.querySelectorAll('.note-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-note-id="${noteId}"]`).classList.add('active');
            
            // 更新编辑器内容（只更新内容，不重新渲染整个编辑器）
            const note = notes[noteId];
            titleInput.value = note.title;
            document.getElementById('editorContent').innerHTML = note.content;
            
            currentNoteId = noteId;
            updateStatus(`已切换到笔记 ${noteId} (总切换次数: ${switchCount}) - 无闪动`);
        }
        
        // 绑定点击事件
        document.addEventListener('click', function(e) {
            const noteItem = e.target.closest('.note-item');
            if (noteItem) {
                const noteId = noteItem.dataset.noteId;
                switchNote(noteId);
            }
        });
        
        // 标题输入事件
        document.getElementById('editorTitle').addEventListener('input', function(e) {
            if (currentNoteId) {
                notes[currentNoteId].title = e.target.value;
                // 更新左侧列表中的标题
                const noteItem = document.querySelector(`[data-note-id="${currentNoteId}"] h3`);
                if (noteItem) {
                    noteItem.textContent = e.target.value || '无标题';
                }
            }
        });
        
        updateStatus('准备就绪 - 点击不同笔记测试平滑切换');
    </script>
</body>
</html>
