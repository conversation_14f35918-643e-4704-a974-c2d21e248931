/* 知识库功能样式 */

/* 知识库主容器 */
.knowledge-base-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.knowledge-base-container.visible {
    opacity: 1;
    visibility: visible;
}

/* 知识库面板 */
.knowledge-base-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 1200px;
    height: 80%;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.knowledge-base-container.visible .knowledge-base-panel {
    transform: scale(1);
}

/* 知识库头部 */
.knowledge-base-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.knowledge-base-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.knowledge-base-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.knowledge-base-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 知识库内容区域 */
.knowledge-base-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 侧边栏 */
.knowledge-sidebar {
    width: 280px;
    background: #f8fafc;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
}

.knowledge-sidebar-nav {
    padding: 16px;
}

.knowledge-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 4px;
    color: #64748b;
    font-size: 14px;
}

.knowledge-nav-item:hover {
    background: #e2e8f0;
    color: #475569;
}

.knowledge-nav-item.active {
    background: #667eea;
    color: white;
}

.knowledge-nav-item .material-symbols-outlined {
    font-size: 20px;
}

/* 主内容区域 */
.knowledge-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 内容滚动区域 */
.knowledge-content-scroll {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0;
    margin: 0;
}

/* 滚动条样式 */
.knowledge-content-scroll::-webkit-scrollbar {
    width: 6px;
}

.knowledge-content-scroll::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.knowledge-content-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.knowledge-content-scroll::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 确保内容区域有足够的最小高度 */
.knowledge-content-scroll {
    min-height: 200px;
}

/* 分析结果卡片间距优化 */
.analysis-card {
    margin-bottom: 20px;
}

.analysis-card:last-child {
    margin-bottom: 0;
}

/* 统计信息网格优化 */
.stats-grid {
    margin-top: 12px;
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
    .knowledge-content-scroll {
        -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
    }
}

/* 搜索区域 */
.knowledge-search-section {
    padding: 24px;
    border-bottom: 1px solid #e5e7eb;
}

.knowledge-search-container {
    position: relative;
    margin-bottom: 16px;
}

.knowledge-search-input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.2s;
}

.knowledge-search-input:focus {
    border-color: #667eea;
}

.knowledge-search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 20px;
}

.knowledge-search-options {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.knowledge-search-type {
    display: flex;
    gap: 8px;
}

.search-type-btn {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.search-type-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.search-type-btn:hover:not(.active) {
    background: #f3f4f6;
}

/* 结果区域 */
.knowledge-results {
    flex: 1;
    overflow-y: auto;
    padding: 0 24px 24px;
}

.knowledge-results-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 16px;
    padding-top: 16px;
}

.knowledge-results-count {
    color: #6b7280;
    font-size: 14px;
}

.knowledge-results-sort {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 搜索结果项 */
.knowledge-result-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.2s;
    cursor: pointer;
}

.knowledge-result-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.knowledge-result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.knowledge-result-title {
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    font-size: 16px;
}

.knowledge-result-score {
    background: #f0f9ff;
    color: #0369a1;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.knowledge-result-preview {
    color: #6b7280;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.knowledge-result-meta {
    display: flex;
    gap: 16px;
    align-items: center;
    font-size: 12px;
    color: #9ca3af;
}

.knowledge-result-folder {
    display: flex;
    align-items: center;
    gap: 4px;
}

.knowledge-result-date {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 分析结果 */
.knowledge-analysis-section {
    padding: 24px;
}

.analysis-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
}

.analysis-card-title {
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.analysis-keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.analysis-keyword {
    background: #f0f9ff;
    color: #0369a1;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.analysis-topics {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.analysis-topic {
    background: #f0fdf4;
    color: #166534;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.analysis-summary {
    color: #4b5563;
    line-height: 1.6;
    font-size: 14px;
}

.analysis-scores {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 12px;
}

.analysis-score-item {
    text-align: center;
}

.analysis-score-value {
    font-size: 24px;
    font-weight: 600;
    color: #667eea;
}

.analysis-score-label {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

/* 关联笔记 */
.knowledge-relations-section {
    padding: 24px;
}

.relations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.relation-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s;
}

.relation-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.relation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.relation-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
}

.relation-similarity {
    background: #fef3c7;
    color: #92400e;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
}

.relation-reason {
    color: #6b7280;
    font-size: 12px;
    line-height: 1.4;
}

/* 加载状态 */
.knowledge-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6b7280;
}

.knowledge-loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.knowledge-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #9ca3af;
    text-align: center;
}

.knowledge-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.knowledge-empty-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: #6b7280;
}

.knowledge-empty-description {
    font-size: 14px;
    line-height: 1.5;
    max-width: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .knowledge-base-panel {
        width: 95%;
        height: 90%;
    }
    
    .knowledge-base-content {
        flex-direction: column;
    }
    
    .knowledge-sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .knowledge-sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding: 12px 16px;
    }
    
    .knowledge-nav-item {
        white-space: nowrap;
        margin-right: 8px;
        margin-bottom: 0;
    }
    
    .analysis-scores {
        grid-template-columns: 1fr;
    }
    
    .relations-grid {
        grid-template-columns: 1fr;
    }
}
