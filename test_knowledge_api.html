<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>🧠 智能知识库 API 测试</h1>
    
    <div class="test-section">
        <h3>🔍 搜索测试</h3>
        <input type="text" id="searchQuery" placeholder="输入搜索内容" value="测试" style="width: 200px; padding: 5px;">
        <select id="searchType" style="padding: 5px;">
            <option value="semantic">语义搜索</option>
            <option value="keyword">关键词搜索</option>
            <option value="hybrid">混合搜索</option>
        </select>
        <button class="test-button" onclick="testSearch()">测试搜索</button>
        <div id="searchResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>📊 分析测试</h3>
        <input type="number" id="analyzeNoteId" placeholder="笔记ID" value="40" style="width: 100px; padding: 5px;">
        <button class="test-button" onclick="testAnalyze()">分析笔记</button>
        <div id="analyzeResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>🔗 关联测试</h3>
        <input type="number" id="relationNoteId" placeholder="笔记ID" value="40" style="width: 100px; padding: 5px;">
        <button class="test-button" onclick="testRelations()">获取关联</button>
        <div id="relationResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>📈 统计测试</h3>
        <button class="test-button" onclick="testStats()">获取统计</button>
        <div id="statsResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        // 模拟的JWT token - 在实际使用中需要从登录获取
        const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjMsInVzZXJuYW1lIjoidGVzdCIsImlhdCI6MTczNzYyNzQ4MSwiZXhwIjoxNzM4MjMyMjgxfQ.example'; // 请替换为实际token

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${TEST_TOKEN}`,
                        ...options.headers
                    }
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `错误: ${result.error || result.data?.message || '未知错误'}`;
            }
        }

        async function testSearch() {
            const query = document.getElementById('searchQuery').value;
            const searchType = document.getElementById('searchType').value;
            
            if (!query.trim()) {
                alert('请输入搜索内容');
                return;
            }

            const result = await makeRequest(`${API_BASE}/knowledge/search`, {
                method: 'POST',
                body: JSON.stringify({
                    query,
                    searchType,
                    options: {
                        limit: 10,
                        threshold: 0.7
                    }
                })
            });

            showResult('searchResult', result);
        }

        async function testAnalyze() {
            const noteId = document.getElementById('analyzeNoteId').value;
            
            if (!noteId) {
                alert('请输入笔记ID');
                return;
            }

            const result = await makeRequest(`${API_BASE}/knowledge/analyze/${noteId}`, {
                method: 'POST'
            });

            showResult('analyzeResult', result);
        }

        async function testRelations() {
            const noteId = document.getElementById('relationNoteId').value;
            
            if (!noteId) {
                alert('请输入笔记ID');
                return;
            }

            const result = await makeRequest(`${API_BASE}/knowledge/relations/${noteId}`);
            showResult('relationResult', result);
        }

        async function testStats() {
            const result = await makeRequest(`${API_BASE}/knowledge/stats`);
            showResult('statsResult', result);
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('知识库API测试页面已加载');
            console.log('请确保:');
            console.log('1. 后端服务运行在 http://localhost:3001');
            console.log('2. 已配置有效的GEMINI_API_KEY');
            console.log('3. 数据库中有测试数据');
            console.log('4. 替换TEST_TOKEN为有效的JWT token');
        };
    </script>
</body>
</html>
