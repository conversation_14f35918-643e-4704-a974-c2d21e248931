<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端搜索测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result-item { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .error { color: red; }
        .success { color: green; }
        .loading { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; width: 300px; margin: 5px; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 前端搜索功能测试</h1>
    
    <div class="test-section">
        <h2>1. 搜索测试</h2>
        <input type="text" id="searchQuery" placeholder="输入搜索内容" value="JVM">
        <button onclick="testSearch('semantic')">语义搜索</button>
        <button onclick="testSearch('keyword')">关键词搜索</button>
        <button onclick="testSearch('hybrid')">混合搜索</button>
        
        <div id="searchStatus"></div>
        <div id="searchResults"></div>
    </div>
    
    <div class="test-section">
        <h2>2. API响应格式测试</h2>
        <div id="apiFormatTest"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 数据解析测试</h2>
        <div id="dataParsingTest"></div>
    </div>

    <script>
        // 模拟API基础URL和token
        const API_BASE_URL = 'http://localhost:3001/api';
        const API_TOKEN = 'your-token-here'; // 需要替换为实际token
        
        let searchResults = [];
        
        // 测试搜索功能
        async function testSearch(searchType) {
            const query = document.getElementById('searchQuery').value;
            const statusDiv = document.getElementById('searchStatus');
            const resultsDiv = document.getElementById('searchResults');
            
            if (!query.trim()) {
                statusDiv.innerHTML = '<div class="error">请输入搜索内容</div>';
                return;
            }
            
            statusDiv.innerHTML = '<div class="loading">正在搜索...</div>';
            resultsDiv.innerHTML = '';
            
            try {
                console.log(`开始${searchType}搜索:`, query);
                
                const response = await fetch(`${API_BASE_URL}/knowledge/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_TOKEN}`
                    },
                    body: JSON.stringify({
                        query,
                        searchType,
                        options: {
                            limit: 20,
                            threshold: 0.7,
                            includeContent: false
                        }
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('原始响应:', result);
                
                // 测试数据解析
                const searchResults = result.data?.results || [];
                console.log('解析后的结果:', searchResults);
                
                statusDiv.innerHTML = `<div class="success">搜索完成！找到 ${searchResults.length} 个结果</div>`;
                
                // 渲染结果
                if (searchResults.length > 0) {
                    resultsDiv.innerHTML = searchResults.map(item => `
                        <div class="result-item">
                            <h3>${escapeHtml(item.title)}</h3>
                            <p><strong>笔记ID:</strong> ${item.noteId}</p>
                            ${item.similarity ? `<p><strong>相似度:</strong> ${Math.round(item.similarity * 100)}%</p>` : ''}
                            ${item.matchedText ? `<p><strong>匹配文本:</strong> ${escapeHtml(item.matchedText.substring(0, 100))}...</p>` : ''}
                            <p><strong>更新时间:</strong> ${new Date(item.updatedAt).toLocaleString()}</p>
                        </div>
                    `).join('');
                } else {
                    resultsDiv.innerHTML = '<div>未找到相关结果</div>';
                }
                
                // 更新其他测试区域
                updateApiFormatTest(result);
                updateDataParsingTest(result);
                
            } catch (error) {
                console.error('搜索错误:', error);
                statusDiv.innerHTML = `<div class="error">搜索失败: ${error.message}</div>`;
                
                // 显示详细错误信息
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>错误详情:</h4>
                        <pre>${error.stack || error.message}</pre>
                    </div>
                `;
            }
        }
        
        // 更新API格式测试
        function updateApiFormatTest(result) {
            const div = document.getElementById('apiFormatTest');
            div.innerHTML = `
                <h4>API响应结构:</h4>
                <pre>${JSON.stringify(result, null, 2)}</pre>
                
                <h4>数据路径验证:</h4>
                <p>✅ result.success: ${result.success}</p>
                <p>✅ result.data: ${result.data ? '存在' : '不存在'}</p>
                <p>✅ result.data.results: ${result.data?.results ? `${result.data.results.length}个结果` : '不存在'}</p>
                <p>✅ result.data.totalCount: ${result.data?.totalCount || 'N/A'}</p>
                <p>✅ result.data.searchTime: ${result.data?.searchTime || 'N/A'}ms</p>
            `;
        }
        
        // 更新数据解析测试
        function updateDataParsingTest(result) {
            const div = document.getElementById('dataParsingTest');
            
            // 模拟前端的数据解析逻辑
            const searchResults = result.data?.results || [];
            
            div.innerHTML = `
                <h4>前端数据解析模拟:</h4>
                <pre>const searchResults = result.data?.results || [];</pre>
                <p>解析结果: ${searchResults.length} 个项目</p>
                
                ${searchResults.length > 0 ? `
                    <h4>第一个结果的字段:</h4>
                    <ul>
                        <li>noteId: ${searchResults[0].noteId}</li>
                        <li>title: ${searchResults[0].title}</li>
                        <li>similarity: ${searchResults[0].similarity || 'N/A'}</li>
                        <li>matchedText: ${searchResults[0].matchedText ? '有' : '无'}</li>
                        <li>folderId: ${searchResults[0].folderId || 'N/A'}</li>
                        <li>updatedAt: ${searchResults[0].updatedAt}</li>
                    </ul>
                ` : '<p>无结果可显示</p>'}
            `;
        }
        
        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('前端搜索测试页面已加载');
            
            // 检查API连接
            document.getElementById('apiFormatTest').innerHTML = `
                <p>📋 测试说明:</p>
                <ul>
                    <li>确保后端服务运行在 ${API_BASE_URL}</li>
                    <li>需要有效的认证token</li>
                    <li>打开浏览器开发者工具查看详细日志</li>
                </ul>
            `;
        });
    </script>
</body>
</html>
