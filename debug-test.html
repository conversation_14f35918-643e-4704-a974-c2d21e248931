<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试 - 下拉菜单</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 400px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .note-item {
            position: relative;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background: white;
        }
        
        .note-item h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .note-item-actions {
            position: absolute;
            right: 16px;
            top: 12px;
            z-index: 100;
        }
        
        .note-item-more {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            font-size: 18px;
            border-radius: 4px;
        }
        
        .note-item-more:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .note-dropdown-menu {
            position: fixed;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 2000;
            min-width: 140px;
            padding: 8px 0;
        }
        
        .note-dropdown-menu.hidden {
            display: none;
        }
        
        .dropdown-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            color: #374151;
            transition: background-color 0.2s;
        }
        
        .dropdown-item:hover {
            background: #f3f4f6;
        }
        
        .dropdown-item-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .status {
            margin-top: 20px;
            padding: 12px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .log {
            margin-top: 10px;
            padding: 8px;
            background: #f8fafc;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>下拉菜单调试测试</h2>
        
        <div class="note-item" data-note-id="1">
            <h3>测试笔记 1</h3>
            <div class="note-item-actions">
                <button class="note-item-more" data-note-id="1" title="更多操作">⋯</button>
                <div class="note-dropdown-menu hidden" id="dropdown-1">
                    <div class="dropdown-item" data-action="rename" data-note-id="1">
                        <div class="dropdown-item-icon">✏️</div>
                        <div class="dropdown-item-text">重命名</div>
                    </div>
                    <div class="dropdown-item" data-action="delete" data-note-id="1">
                        <div class="dropdown-item-icon">🗑️</div>
                        <div class="dropdown-item-text">删除</div>
                    </div>
                </div>
            </div>
            <div>这是第一个测试笔记的内容...</div>
        </div>
        
        <div class="note-item" data-note-id="2">
            <h3>测试笔记 2</h3>
            <div class="note-item-actions">
                <button class="note-item-more" data-note-id="2" title="更多操作">⋯</button>
                <div class="note-dropdown-menu hidden" id="dropdown-2">
                    <div class="dropdown-item" data-action="rename" data-note-id="2">
                        <div class="dropdown-item-icon">✏️</div>
                        <div class="dropdown-item-text">重命名</div>
                    </div>
                    <div class="dropdown-item" data-action="delete" data-note-id="2">
                        <div class="dropdown-item-icon">🗑️</div>
                        <div class="dropdown-item-text">删除</div>
                    </div>
                </div>
            </div>
            <div>这是第二个测试笔记的内容...</div>
        </div>
        
        <div class="status">
            <strong>测试状态：</strong> <span id="status">等待测试...</span>
        </div>
        
        <div class="log" id="log">
            <div>点击日志将显示在这里...</div>
        </div>
    </div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `<div>[${time}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        function updateStatus(text) {
            status.textContent = text;
        }
        
        // 下拉菜单处理
        document.addEventListener('click', function(e) {
            addLog(`点击事件: ${e.target.tagName} ${e.target.className}`);
            
            const moreBtn = e.target.closest('.note-item-more');
            if (moreBtn) {
                addLog('检测到下拉菜单按钮点击');
                e.stopPropagation();
                e.preventDefault();
                
                const noteId = moreBtn.dataset.noteId;
                const dropdown = document.getElementById(`dropdown-${noteId}`);
                
                addLog(`noteId: ${noteId}, dropdown存在: ${!!dropdown}`);
                
                // 隐藏其他下拉菜单
                document.querySelectorAll('.note-dropdown-menu').forEach(menu => {
                    if (menu.id !== `dropdown-${noteId}`) {
                        menu.classList.add('hidden');
                    }
                });
                
                if (dropdown) {
                    // 计算位置
                    const rect = moreBtn.getBoundingClientRect();
                    dropdown.style.left = `${rect.left - 100}px`;
                    dropdown.style.top = `${rect.bottom + 5}px`;
                    
                    const wasHidden = dropdown.classList.contains('hidden');
                    dropdown.classList.toggle('hidden');
                    
                    addLog(`下拉菜单状态: ${wasHidden ? '显示' : '隐藏'}`);
                    updateStatus(wasHidden ? '下拉菜单已显示' : '下拉菜单已隐藏');
                }
                return false;
            }
            
            // 下拉菜单项点击
            const dropdownItem = e.target.closest('.dropdown-item');
            if (dropdownItem) {
                addLog('检测到下拉菜单项点击');
                e.stopPropagation();
                
                const action = dropdownItem.dataset.action;
                const noteId = dropdownItem.dataset.noteId;
                
                addLog(`操作: ${action}, noteId: ${noteId}`);
                updateStatus(`执行操作: ${action}`);
                
                // 隐藏下拉菜单
                document.getElementById(`dropdown-${noteId}`)?.classList.add('hidden');
                return false;
            }
            
            // 点击其他地方隐藏所有下拉菜单
            if (!e.target.closest('.note-dropdown-menu')) {
                const openMenus = document.querySelectorAll('.note-dropdown-menu:not(.hidden)');
                if (openMenus.length > 0) {
                    addLog('隐藏所有下拉菜单');
                    openMenus.forEach(menu => menu.classList.add('hidden'));
                    updateStatus('下拉菜单已隐藏');
                }
            }
        });
        
        addLog('页面加载完成，事件监听器已设置');
        updateStatus('准备就绪');
    </script>
</body>
</html>
