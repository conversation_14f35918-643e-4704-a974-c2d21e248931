<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码修改功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #3a80d2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>密码修改功能测试</h1>
        <p>这个页面用于测试密码修改API功能</p>
        
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" placeholder="请输入用户名">
        </div>
        
        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" id="password" placeholder="请输入密码">
        </div>
        
        <button onclick="login()">登录</button>
        
        <hr style="margin: 30px 0;">
        
        <h2>修改密码</h2>
        <div class="form-group">
            <label for="currentPassword">当前密码</label>
            <input type="password" id="currentPassword" placeholder="请输入当前密码">
        </div>
        
        <div class="form-group">
            <label for="newPassword">新密码</label>
            <input type="password" id="newPassword" placeholder="请输入新密码">
        </div>
        
        <button onclick="changePassword()">修改密码</button>
        <button onclick="verifyCurrentPassword()">验证当前密码</button>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        let token = localStorage.getItem('token');
        
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:3001/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    token = data.token;
                    localStorage.setItem('token', token);
                    showResult(`登录成功！用户：${data.user.username}`, 'success');
                } else {
                    showResult(`登录失败：${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`登录错误：${error.message}`, 'error');
            }
        }
        
        async function verifyCurrentPassword() {
            if (!token) {
                showResult('请先登录', 'error');
                return;
            }

            const currentPassword = document.getElementById('currentPassword').value;

            if (!currentPassword) {
                showResult('请输入当前密码', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:3001/auth/verify-current-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ currentPassword })
                });

                const data = await response.json();

                if (response.ok) {
                    showResult(`当前密码验证成功：${data.message}`, 'success');
                } else {
                    showResult(`当前密码验证失败：${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`当前密码验证错误：${error.message}`, 'error');
            }
        }

        async function changePassword() {
            if (!token) {
                showResult('请先登录', 'error');
                return;
            }
            
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            
            if (!currentPassword || !newPassword) {
                showResult('请输入当前密码和新密码', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:3001/auth/change-password', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ currentPassword, newPassword })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`密码修改成功：${data.message}`, 'success');
                    document.getElementById('currentPassword').value = '';
                    document.getElementById('newPassword').value = '';
                } else {
                    showResult(`密码修改失败：${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`密码修改错误：${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 5000);
        }
        
        // 页面加载时检查是否已登录
        if (token) {
            showResult('已检测到登录状态', 'success');
        }
    </script>
</body>
</html>
