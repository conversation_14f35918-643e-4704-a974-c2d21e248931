# 🧠 智能知识库功能使用指南

## 📋 功能概述

智能知识库是基于AI技术的笔记管理和搜索系统，提供以下核心功能：

### 🔍 **语义搜索**
- **基于内容含义搜索**：不仅仅匹配关键词，而是理解内容的语义
- **多种搜索模式**：语义搜索、关键词搜索、混合搜索
- **智能相似度匹配**：根据内容相似度排序结果

### 📊 **内容分析**
- **AI摘要生成**：自动生成笔记摘要
- **关键词提取**：识别重要关键词
- **主题分类**：自动分类笔记主题
- **情感分析**：分析内容情感倾向
- **复杂度评估**：评估内容复杂程度

### 🔗 **关联发现**
- **自动关联**：发现笔记间的相似性和关联性
- **关系类型**：相似、引用、后续、矛盾、补充等
- **关联推荐**：推荐相关笔记

### 📈 **统计分析**
- **使用统计**：分析笔记使用情况
- **分类分布**：查看笔记分类分布
- **关键词云**：可视化常用关键词

## 🚀 快速开始

### 1. **访问知识库**
- 在笔记编辑界面，点击右下角的 🧠 **智能知识库** 按钮
- 或者使用快捷键（如果配置了的话）

### 2. **首次使用准备**
在使用知识库功能前，需要进行初始化处理：

```bash
# 在后端目录执行批量处理
cd bankend
node -e "
const { EmbeddingService } = require('./services/embeddingService');
const { AnalysisService } = require('./services/analysisService');

async function init() {
    const embedding = new EmbeddingService();
    const analysis = new AnalysisService();
    
    // 替换为你的用户ID
    const userId = 3;
    
    console.log('开始批量处理...');
    await embedding.processAllUserNotes(userId);
    await analysis.analyzeAllUserNotes(userId);
    console.log('处理完成！');
}

init().catch(console.error);
"
```

### 3. **配置Gemini API密钥**
确保在 `bankend/.env` 文件中配置了Gemini API密钥：

```env
GEMINI_API_KEY=your_gemini_api_key_here
```

## 📖 详细使用说明

### 🔍 **语义搜索使用**

1. **打开知识库面板**
   - 点击编辑器右下角的 🧠 按钮

2. **选择搜索类型**
   - **语义搜索**：基于内容含义搜索（推荐）
   - **关键词搜索**：传统关键词匹配
   - **混合搜索**：结合两种方式

3. **输入搜索内容**
   - 在搜索框中输入你要查找的内容
   - 支持自然语言描述，如"关于项目管理的笔记"

4. **查看搜索结果**
   - 结果按相似度排序
   - 显示匹配度百分比
   - 点击结果可直接跳转到对应笔记

### 📊 **内容分析功能**

1. **分析当前笔记**
   - 在知识库面板中切换到"内容分析"标签
   - 点击"分析当前笔记"按钮

2. **查看分析结果**
   - **摘要**：AI生成的内容摘要
   - **关键词**：提取的重要关键词
   - **主题标签**：识别的主题分类
   - **情感分数**：内容情感倾向（-1到1）
   - **复杂度**：内容复杂程度（0到1）

### 🔗 **关联发现**

1. **查看笔记关联**
   - 选择一个笔记后，在知识库面板查看"关联发现"
   - 系统会显示与当前笔记相关的其他笔记

2. **关联类型说明**
   - **相似**：内容相似的笔记
   - **引用**：被引用或引用其他笔记
   - **后续**：后续相关内容
   - **矛盾**：观点相反的内容
   - **补充**：补充说明的内容

### 📈 **统计分析**

查看整体的笔记统计信息：
- 总分析笔记数量
- 平均情感分数
- 平均复杂度
- 分类分布
- 热门关键词

## 🛠️ API接口说明

### 搜索接口
```javascript
// 语义搜索
POST /api/knowledge/search
{
    "query": "搜索内容",
    "searchType": "semantic",
    "options": {
        "limit": 20,
        "threshold": 0.7
    }
}
```

### 分析接口
```javascript
// 分析笔记
POST /api/knowledge/analyze/:noteId

// 获取分析结果
GET /api/knowledge/analysis/:noteId
```

### 批量处理接口
```javascript
// 批量处理
POST /api/knowledge/batch/process
{
    "operations": ["vectorize", "analyze", "relations", "tags"]
}
```

## ⚙️ 配置选项

### 环境变量配置
```env
# Gemini API密钥（必需）
GEMINI_API_KEY=your_api_key

# 数据库配置
DB_HOST=your_db_host
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_DATABASE=notes

# JWT配置
JWT_SECRET=your_jwt_secret
```

### 知识库参数
- **相似度阈值**：默认0.7，可调整搜索敏感度
- **搜索结果数量**：默认20条
- **文本分块大小**：默认1000字符
- **分块重叠**：默认200字符

## 🔧 故障排除

### 常见问题

1. **搜索无结果**
   - 确保笔记已经过向量化处理
   - 尝试降低相似度阈值
   - 检查Gemini API密钥是否正确

2. **分析失败**
   - 检查网络连接
   - 确认API密钥有效
   - 查看服务器日志

3. **批量处理缓慢**
   - 这是正常现象，AI处理需要时间
   - 可以分批处理，避免一次处理太多笔记

### 性能优化

1. **定期清理**
   - 删除不需要的向量数据
   - 清理过期的搜索历史

2. **合理使用**
   - 避免频繁的批量处理
   - 使用增量更新而非全量处理

## 📚 最佳实践

### 1. **搜索技巧**
- 使用自然语言描述你要找的内容
- 尝试不同的关键词组合
- 利用混合搜索获得更全面的结果

### 2. **内容组织**
- 保持笔记内容的质量和完整性
- 使用清晰的标题和结构
- 定期整理和更新笔记

### 3. **系统维护**
- 定期运行批量分析
- 监控系统性能
- 及时更新API密钥

## 🔮 未来功能

计划中的功能增强：
- **知识图谱可视化**：图形化展示笔记关联
- **智能推荐**：基于使用习惯推荐相关内容
- **协作功能**：团队知识库共享
- **多语言支持**：支持更多语言的分析
- **自定义模型**：支持自定义AI模型

## 📞 技术支持

如果遇到问题或需要帮助：
1. 查看服务器日志获取详细错误信息
2. 检查数据库连接和表结构
3. 验证API密钥和网络连接
4. 参考本文档的故障排除部分

---

**注意**：智能知识库功能需要网络连接和有效的Gemini API密钥才能正常工作。首次使用时需要进行批量处理，这可能需要一些时间，请耐心等待。
