<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复测试 - 智能笔记</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="index.css">
    <style>
        /* 测试页面特定样式 */
        .test-container {
            height: 100vh;
            overflow: hidden;
        }
        
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(37, 99, 235, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 3000;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="test-info">
        测试修复：下拉菜单定位 | 标题保持 | 无闪动效果
    </div>
    
    <div class="test-container">
        <div class="app-container sidebar-mode-full">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <!-- 用户资料 -->
                <div class="sidebar-profile">
                    <div class="avatar">
                        <span class="material-symbols-outlined">person</span>
                    </div>
                    <div class="username">admin</div>
                    <div class="vip-status">💎 VIP</div>
                </div>

                <!-- 新建按钮 -->
                <div class="sidebar-actions">
                    <button class="new-note-btn">
                        <span class="material-symbols-outlined">add_circle</span>
                        新建
                    </button>
                </div>

                <!-- 导航菜单 -->
                <nav class="sidebar-nav">
                    <div class="nav-item active" data-title="最新">
                        <span class="material-symbols-outlined">schedule</span>
                        <span class="item-name">最新</span>
                    </div>
                    
                    <div class="folder-item" data-title="工作">
                        <span class="material-symbols-outlined">work</span>
                        <span class="item-name">工作</span>
                        <span class="folder-arrow material-symbols-outlined">expand_more</span>
                    </div>
                    
                    <div class="folder-item" data-title="学习">
                        <span class="material-symbols-outlined">school</span>
                        <span class="item-name">学习</span>
                        <span class="folder-arrow material-symbols-outlined">expand_more</span>
                    </div>
                </nav>
            </aside>

            <!-- 笔记列表面板 -->
            <div class="note-list-panel">
                <div class="note-list-header">
                    <input type="text" class="search-bar" placeholder="搜索笔记...">
                </div>
                
                <div class="note-list">
                    <div class="note-item active" data-note-id="1">
                        <h3>我的自定义标题</h3>
                        <div class="note-item-actions">
                            <button class="note-item-more" data-note-id="1" title="更多操作">⋯</button>
                            <div class="note-dropdown-menu hidden" id="dropdown-1">
                                <div class="dropdown-item" data-action="rename" data-note-id="1">
                                    <div class="dropdown-item-icon"><span class="material-symbols-outlined">edit</span></div>
                                    <div class="dropdown-item-text">重命名</div>
                                </div>
                                <div class="dropdown-item" data-action="delete" data-note-id="1">
                                    <div class="dropdown-item-icon"><span class="material-symbols-outlined">delete</span></div>
                                    <div class="dropdown-item-text">删除</div>
                                </div>
                                <div class="dropdown-item" data-action="move" data-note-id="1">
                                    <div class="dropdown-item-icon"><span class="material-symbols-outlined">drive_file_move</span></div>
                                    <div class="dropdown-item-text">移动到</div>
                                </div>
                            </div>
                        </div>
                        <div class="note-item-snippet">这是一个测试笔记，用来验证标题不会被重置...</div>
                        <div class="note-item-meta">2024-01-15 · 工作</div>
                    </div>
                    
                    <div class="note-item" data-note-id="2">
                        <h3>另一个自定义标题</h3>
                        <div class="note-item-actions">
                            <button class="note-item-more" data-note-id="2" title="更多操作">⋯</button>
                            <div class="note-dropdown-menu hidden" id="dropdown-2">
                                <div class="dropdown-item" data-action="rename" data-note-id="2">
                                    <div class="dropdown-item-icon"><span class="material-symbols-outlined">edit</span></div>
                                    <div class="dropdown-item-text">重命名</div>
                                </div>
                                <div class="dropdown-item" data-action="delete" data-note-id="2">
                                    <div class="dropdown-item-icon"><span class="material-symbols-outlined">delete</span></div>
                                    <div class="dropdown-item-text">删除</div>
                                </div>
                            </div>
                        </div>
                        <div class="note-item-snippet">点击其他笔记时，当前笔记的标题应该保持不变...</div>
                        <div class="note-item-meta">2024-01-14 · 学习</div>
                    </div>
                    
                    <div class="note-item" data-note-id="3">
                        <h3>第三个测试笔记</h3>
                        <div class="note-item-actions">
                            <button class="note-item-more" data-note-id="3" title="更多操作">⋯</button>
                            <div class="note-dropdown-menu hidden" id="dropdown-3">
                                <div class="dropdown-item" data-action="rename" data-note-id="3">
                                    <div class="dropdown-item-icon"><span class="material-symbols-outlined">edit</span></div>
                                    <div class="dropdown-item-text">重命名</div>
                                </div>
                                <div class="dropdown-item" data-action="delete" data-note-id="3">
                                    <div class="dropdown-item-icon"><span class="material-symbols-outlined">delete</span></div>
                                    <div class="dropdown-item-text">删除</div>
                                </div>
                            </div>
                        </div>
                        <div class="note-item-snippet">测试下拉菜单不会被遮挡，应该正确显示在按钮下方...</div>
                        <div class="note-item-meta">2024-01-13 · 日常</div>
                    </div>
                </div>
            </div>

            <!-- 编辑器面板 -->
            <div class="editor-panel">
                <div class="editor-header">
                    <div class="editor-header-title">
                        <input type="text" class="editor-title-input" value="我的自定义标题" placeholder="无标题" id="title-input">
                    </div>
                    <div class="editor-header-actions">
                        <div class="save-status">
                            <span class="material-symbols-outlined">check_circle</span>
                            已保存
                        </div>
                    </div>
                </div>

                <div class="editor-content-wrapper">
                    <div class="editor-content" contenteditable="true">
                        <h1>测试修复效果</h1>
                        <p>这个页面用来测试以下修复：</p>
                        
                        <h2>1. 下拉菜单定位修复</h2>
                        <p>点击笔记项目右侧的"⋯"按钮，下拉菜单应该正确显示，不会被其他元素遮挡。</p>
                        
                        <h2>2. 标题保持修复</h2>
                        <p>修改标题后，点击其他笔记再回来，标题应该保持你修改的内容，不会被重置为"无标题笔记"。</p>
                        
                        <h2>3. 移除闪动效果</h2>
                        <p>点击不同的笔记项目时，应该没有闪动的动画效果，切换更加流畅自然。</p>
                        
                        <h2>测试步骤</h2>
                        <ol>
                            <li>修改上方的标题为自定义内容</li>
                            <li>点击左侧其他笔记项目</li>
                            <li>再点击回第一个笔记</li>
                            <li>检查标题是否保持你的修改</li>
                            <li>点击笔记项目的"⋯"按钮测试下拉菜单</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试页面的交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            let currentNoteId = '1';
            const noteTitles = {
                '1': '我的自定义标题',
                '2': '另一个自定义标题', 
                '3': '第三个测试笔记'
            };
            
            // 笔记项目点击效果
            const noteItems = document.querySelectorAll('.note-item');
            noteItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    if (e.target.closest('.note-item-more') || e.target.closest('.note-dropdown-menu')) {
                        return;
                    }
                    
                    const noteId = this.dataset.noteId;
                    if (noteId && noteId !== currentNoteId) {
                        // 保存当前标题
                        const titleInput = document.getElementById('title-input');
                        if (titleInput && currentNoteId) {
                            noteTitles[currentNoteId] = titleInput.value;
                        }
                        
                        // 切换选中状态
                        noteItems.forEach(i => i.classList.remove('active'));
                        this.classList.add('active');
                        
                        // 更新编辑器标题
                        if (titleInput) {
                            titleInput.value = noteTitles[noteId] || '无标题';
                        }
                        
                        currentNoteId = noteId;
                    }
                });
            });
            
            // 下拉菜单处理
            document.addEventListener('click', function(e) {
                console.log('Click event:', e.target);

                const moreBtn = e.target.closest('.note-item-more');
                if (moreBtn) {
                    console.log('More button clicked:', moreBtn);
                    e.stopPropagation();
                    e.preventDefault();

                    const noteId = moreBtn.dataset.noteId;
                    const dropdown = document.getElementById(`dropdown-${noteId}`);
                    console.log('Dropdown element:', dropdown);

                    // 隐藏其他下拉菜单
                    document.querySelectorAll('.note-dropdown-menu').forEach(menu => {
                        if (menu.id !== `dropdown-${noteId}`) {
                            menu.classList.add('hidden');
                        }
                    });

                    if (dropdown) {
                        // 计算位置
                        const rect = moreBtn.getBoundingClientRect();
                        dropdown.style.left = `${rect.left - 100}px`;
                        dropdown.style.top = `${rect.bottom + 5}px`;
                        dropdown.classList.toggle('hidden');
                        console.log('Dropdown toggled, hidden:', dropdown.classList.contains('hidden'));
                    }
                    return false;
                } else if (!e.target.closest('.note-dropdown-menu')) {
                    // 点击其他地方隐藏所有下拉菜单
                    document.querySelectorAll('.note-dropdown-menu').forEach(menu => {
                        menu.classList.add('hidden');
                    });
                }
            });
            
            // 标题输入处理
            const titleInput = document.getElementById('title-input');
            if (titleInput) {
                titleInput.addEventListener('input', function() {
                    if (currentNoteId) {
                        noteTitles[currentNoteId] = this.value;
                        // 更新笔记列表中的标题
                        const noteItem = document.querySelector(`[data-note-id="${currentNoteId}"] h3`);
                        if (noteItem) {
                            noteItem.textContent = this.value || '无标题';
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
