/**
 * 密码管理功能演示脚本
 * 这个脚本演示了如何使用新增的密码管理API
 */

const API_BASE_URL = 'http://localhost:3001';

class PasswordManagementDemo {
    constructor() {
        this.token = null;
    }

    /**
     * 用户登录
     */
    async login(username, password) {
        try {
            console.log(`🔐 正在登录用户: ${username}`);
            
            const response = await fetch(`${API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                this.token = data.token;
                console.log(`✅ 登录成功! 用户: ${data.user.username}`);
                console.log(`🎫 Token: ${this.token.substring(0, 20)}...`);
                return true;
            } else {
                console.error(`❌ 登录失败: ${data.message}`);
                return false;
            }
        } catch (error) {
            console.error(`💥 登录错误: ${error.message}`);
            return false;
        }
    }

    /**
     * 修改密码
     */
    async changePassword(currentPassword, newPassword) {
        if (!this.token) {
            console.error('❌ 请先登录');
            return false;
        }

        try {
            console.log('🔄 正在修改密码...');
            
            const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                },
                body: JSON.stringify({ currentPassword, newPassword })
            });

            const data = await response.json();

            if (response.ok) {
                console.log(`✅ 密码修改成功: ${data.message}`);
                return true;
            } else {
                console.error(`❌ 密码修改失败: ${data.message}`);
                return false;
            }
        } catch (error) {
            console.error(`💥 密码修改错误: ${error.message}`);
            return false;
        }
    }

    /**
     * 验证token
     */
    async verifyToken() {
        if (!this.token) {
            console.error('❌ 没有可验证的token');
            return false;
        }

        try {
            console.log('🔍 正在验证token...');
            
            const response = await fetch(`${API_BASE_URL}/auth/verify`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            const data = await response.json();

            if (response.ok) {
                console.log(`✅ Token验证成功! 用户: ${data.user.username}`);
                return true;
            } else {
                console.error(`❌ Token验证失败: ${data.message}`);
                return false;
            }
        } catch (error) {
            console.error(`💥 Token验证错误: ${error.message}`);
            return false;
        }
    }

    /**
     * 完整的密码修改演示流程
     */
    async demonstratePasswordChange(username, oldPassword, newPassword) {
        console.log('🚀 开始密码管理功能演示');
        console.log('=' .repeat(50));

        // 步骤1: 登录
        console.log('\n📝 步骤1: 用户登录');
        const loginSuccess = await this.login(username, oldPassword);
        if (!loginSuccess) {
            console.log('❌ 演示终止: 登录失败');
            return;
        }

        // 步骤2: 验证当前token
        console.log('\n📝 步骤2: 验证当前登录状态');
        await this.verifyToken();

        // 步骤3: 修改密码
        console.log('\n📝 步骤3: 修改密码');
        const changeSuccess = await this.changePassword(oldPassword, newPassword);
        if (!changeSuccess) {
            console.log('❌ 密码修改失败');
            return;
        }

        // 步骤4: 验证新密码是否生效
        console.log('\n📝 步骤4: 验证新密码');
        console.log('🔄 使用新密码重新登录...');
        
        // 清除旧token
        this.token = null;
        
        const newLoginSuccess = await this.login(username, newPassword);
        if (newLoginSuccess) {
            console.log('✅ 新密码验证成功!');
            
            // 可选: 将密码改回原来的密码
            console.log('\n📝 步骤5: 恢复原密码 (可选)');
            await this.changePassword(newPassword, oldPassword);
        } else {
            console.log('❌ 新密码验证失败');
        }

        console.log('\n🎉 密码管理功能演示完成!');
        console.log('=' .repeat(50));
    }
}

// 使用示例
async function runDemo() {
    const demo = new PasswordManagementDemo();
    
    // 请根据实际情况修改这些参数
    const username = 'testuser';  // 替换为实际的用户名
    const oldPassword = 'oldpass123';  // 替换为实际的当前密码
    const newPassword = 'newpass456';  // 替换为新密码

    await demo.demonstratePasswordChange(username, oldPassword, newPassword);
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PasswordManagementDemo;
    
    // 如果直接运行此脚本
    if (require.main === module) {
        // 需要安装 node-fetch: npm install node-fetch
        const fetch = require('node-fetch');
        global.fetch = fetch;
        
        runDemo().catch(console.error);
    }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.PasswordManagementDemo = PasswordManagementDemo;
    window.runDemo = runDemo;
    
    console.log('💡 密码管理演示类已加载到 window.PasswordManagementDemo');
    console.log('💡 运行 runDemo() 开始演示');
}
