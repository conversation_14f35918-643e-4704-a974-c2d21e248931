#!/bin/bash

# 系统服务配置脚本
# 配置防火墙、开机自启动、SSL 证书等

set -e

echo "=== 系统服务配置开始 ==="

# 1. 配置防火墙
echo "1. 配置防火墙..."

# 启动防火墙服务
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 开放必要端口
echo "开放 HTTP 端口 (80)..."
sudo firewall-cmd --permanent --add-service=http

echo "开放 HTTPS 端口 (443)..."
sudo firewall-cmd --permanent --add-service=https

echo "开放后端端口 (3001) - 仅本地访问..."
sudo firewall-cmd --permanent --add-port=3001/tcp

# 如果需要 SSH 访问
echo "确保 SSH 端口开放..."
sudo firewall-cmd --permanent --add-service=ssh

# 重载防火墙规则
sudo firewall-cmd --reload

echo "防火墙配置完成"

# 2. 设置服务开机自启动
echo ""
echo "2. 设置服务开机自启动..."

# MySQL
sudo systemctl enable mysqld
echo "MySQL 开机自启动已设置"

# Nginx
sudo systemctl enable nginx
echo "Nginx 开机自启动已设置"

# PM2 (需要用户手动运行 pm2 startup 命令)
echo ""
echo "PM2 开机自启动设置："
echo "请运行以下命令来设置 PM2 开机自启动："
echo "pm2 startup"
echo "然后运行显示的命令"

# 3. 创建系统监控脚本
echo ""
echo "3. 创建系统监控脚本..."

cat > /var/www/notes/monitor.sh << 'EOF'
#!/bin/bash

# 系统监控脚本
# 检查各个服务的运行状态

echo "=== Notes 应用状态监控 ==="
echo "时间: $(date)"
echo ""

# 检查 MySQL
echo "MySQL 状态:"
if systemctl is-active --quiet mysqld; then
    echo "✓ MySQL 运行正常"
else
    echo "✗ MySQL 未运行"
fi

# 检查 Nginx
echo ""
echo "Nginx 状态:"
if systemctl is-active --quiet nginx; then
    echo "✓ Nginx 运行正常"
else
    echo "✗ Nginx 未运行"
fi

# 检查后端应用
echo ""
echo "后端应用状态:"
if pm2 list | grep -q "notes-backend.*online"; then
    echo "✓ 后端应用运行正常"
else
    echo "✗ 后端应用未运行"
fi

# 检查端口占用
echo ""
echo "端口占用情况:"
echo "端口 80 (HTTP): $(sudo netstat -tlnp | grep :80 | wc -l) 个进程"
echo "端口 443 (HTTPS): $(sudo netstat -tlnp | grep :443 | wc -l) 个进程"
echo "端口 3001 (后端): $(sudo netstat -tlnp | grep :3001 | wc -l) 个进程"
echo "端口 3306 (MySQL): $(sudo netstat -tlnp | grep :3306 | wc -l) 个进程"

# 检查 AI 配置
echo ""
echo "AI 功能状态:"
if [ -f "/var/www/notes/frontend/.env" ]; then
    if grep -q "GEMINI_API_KEY=" /var/www/notes/frontend/.env; then
        api_key=$(grep "GEMINI_API_KEY=" /var/www/notes/frontend/.env | cut -d'=' -f2)
        if [ "$api_key" != "AIzaSyCxcYEGemEa3nbKU1JwByF0hdq_RYP04_g" ] && [ -n "$api_key" ]; then
            echo "✓ Gemini API 密钥已配置"
        else
            echo "✗ Gemini API 密钥未配置"
        fi
    else
        echo "✗ 未找到 Gemini API 配置"
    fi
else
    echo "✗ 未找到前端环境变量文件"
fi

# 检查磁盘空间
echo ""
echo "磁盘使用情况:"
df -h /var/www/notes

# 检查内存使用
echo ""
echo "内存使用情况:"
free -h

echo ""
echo "=== 监控完成 ==="
EOF

chmod +x /var/www/notes/monitor.sh

# 4. 创建日志轮转配置
echo ""
echo "4. 配置日志轮转..."

sudo tee /etc/logrotate.d/notes << 'EOF'
/var/log/notes/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 notes notes
    postrotate
        pm2 reloadLogs
    endscript
}
EOF

echo "日志轮转配置完成"

# 5. 创建备份脚本
echo ""
echo "5. 创建备份脚本..."

cat > /var/www/notes/backup.sh << 'EOF'
#!/bin/bash

# 数据库和文件备份脚本

BACKUP_DIR="/var/backups/notes"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p "$BACKUP_DIR"

echo "开始备份 - $DATE"

# 备份数据库
echo "备份数据库..."
mysqldump -u notes_user -p notes_db > "$BACKUP_DIR/database_$DATE.sql"

# 备份应用文件
echo "备份应用文件..."
tar -czf "$BACKUP_DIR/app_$DATE.tar.gz" -C /var/www/notes .

# 清理旧备份（保留 7 天）
echo "清理旧备份..."
find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete

echo "备份完成 - $DATE"
EOF

chmod +x /var/www/notes/backup.sh

# 6. 设置定时任务
echo ""
echo "6. 设置定时任务..."
echo "建议添加以下定时任务到 crontab (crontab -e):"
echo ""
echo "# 每天凌晨 2 点备份"
echo "0 2 * * * /var/www/notes/backup.sh >> /var/log/notes/backup.log 2>&1"
echo ""
echo "# 每小时检查服务状态"
echo "0 * * * * /var/www/notes/monitor.sh >> /var/log/notes/monitor.log 2>&1"

# 7. SSL 证书配置提示
echo ""
echo "7. SSL 证书配置 (可选):"
echo ""
echo "如果需要 HTTPS，可以使用 Let's Encrypt 免费证书:"
echo ""
echo "安装 Certbot:"
echo "sudo yum install -y certbot python2-certbot-nginx"
echo ""
echo "获取证书:"
echo "sudo certbot --nginx -d your-domain.com"
echo ""
echo "设置自动续期:"
echo "echo '0 12 * * * /usr/bin/certbot renew --quiet' | sudo crontab -"

echo ""
echo "=== 系统服务配置完成 ==="
echo ""
echo "配置摘要:"
echo "- 防火墙已配置并开放必要端口"
echo "- MySQL 和 Nginx 已设置开机自启动"
echo "- 创建了监控脚本: /var/www/notes/monitor.sh"
echo "- 创建了备份脚本: /var/www/notes/backup.sh"
echo "- 配置了日志轮转"
echo ""
echo "下一步:"
echo "1. 运行 'pm2 startup' 设置 PM2 开机自启动"
echo "2. 设置定时任务 (crontab -e)"
echo "3. 配置 SSL 证书 (可选)"
