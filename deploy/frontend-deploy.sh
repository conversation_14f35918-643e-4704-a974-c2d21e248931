#!/bin/bash

# 前端部署脚本
# 用于构建和部署前端应用

set -e

echo "=== 前端部署开始 ==="

# 检查当前目录
if [ ! -f "package.json" ] || [ ! -f "vite.config.ts" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查 Node.js 和 npm
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "错误: 未找到 npm"
    exit 1
fi

# 配置前端环境变量
echo "1. 配置前端环境变量..."
if [ -f "deploy/frontend.env" ]; then
    cp "deploy/frontend.env" .env
    echo "已复制前端环境变量文件"

    echo ""
    echo "请编辑 .env 文件，配置以下内容："
    echo "- GEMINI_API_KEY: Google Gemini API 密钥"
    echo ""
    read -p "是否现在编辑 .env 文件？(y/n): " edit_env

    if [ "$edit_env" = "y" ]; then
        vim .env
    fi
else
    echo "警告: 未找到前端环境变量模板文件"
    echo "请确保 .env 文件包含 GEMINI_API_KEY 配置"

    if [ ! -f ".env" ]; then
        echo "创建默认 .env 文件..."
        cat > .env << 'EOF'
# Gemini AI API Configuration
# 请在这里设置你的 Google Gemini API 密钥
# 获取API密钥: https://aistudio.google.com/app/apikey

GEMINI_API_KEY=AIzaSyCxcYEGemEa3nbKU1JwByF0hdq_RYP04_g
API_KEY=AIzaSyCxcYEGemEa3nbKU1JwByF0hdq_RYP04_g

# 注意：
# 1. 请将 your_gemini_api_key_here 替换为你的实际API密钥
# 2. 不要将包含真实API密钥的.env文件提交到版本控制系统
# 3. 确保将.env文件添加到.gitignore中
EOF
        echo "已创建默认 .env 文件，请编辑并添加你的 API 密钥"
        read -p "是否现在编辑 .env 文件？(y/n): " edit_env

        if [ "$edit_env" = "y" ]; then
            vim .env
        fi
    fi
fi

# 安装依赖
echo "2. 安装前端依赖..."
npm install

# 构建前端项目
echo "3. 构建前端项目..."
npm run build

# 检查构建结果
if [ ! -d "dist" ]; then
    echo "错误: 构建失败，未找到 dist 目录"
    echo "请检查："
    echo "1. .env 文件是否正确配置了 GEMINI_API_KEY"
    echo "2. 网络连接是否正常"
    echo "3. Node.js 版本是否兼容"
    exit 1
fi

# 创建前端部署目录
FRONTEND_DIR="/var/www/notes/frontend"
echo "4. 创建前端部署目录..."
sudo mkdir -p "$FRONTEND_DIR"
sudo chown -R $USER:$USER /var/www/notes

# 备份旧版本（如果存在）
if [ -d "$FRONTEND_DIR/dist" ]; then
    echo "5. 备份旧版本..."
    sudo mv "$FRONTEND_DIR/dist" "$FRONTEND_DIR/dist.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 复制构建文件
echo "6. 复制构建文件..."
cp -r dist "$FRONTEND_DIR/"

# 复制环境变量文件到部署目录（用于后续更新）
if [ -f ".env" ]; then
    cp .env "$FRONTEND_DIR/"
    echo "已复制环境变量文件到部署目录"
fi

# 设置正确的权限
sudo chown -R nginx:nginx "$FRONTEND_DIR/dist"
sudo chmod -R 755 "$FRONTEND_DIR/dist"

# 配置 Nginx
echo "7. 配置 Nginx..."
NGINX_CONF="/etc/nginx/conf.d/notes.conf"

if [ -f "../deploy/nginx.conf" ]; then
    sudo cp "../deploy/nginx.conf" "$NGINX_CONF"
    echo "已复制 Nginx 配置文件"
    
    echo ""
    echo "请编辑 Nginx 配置文件，修改以下配置："
    echo "- server_name: 替换为你的域名"
    echo "- SSL 证书路径（如果使用 HTTPS）"
    echo ""
    read -p "是否现在编辑 Nginx 配置？(y/n): " edit_nginx
    
    if [ "$edit_nginx" = "y" ]; then
        sudo vim "$NGINX_CONF"
    fi
else
    echo "警告: 未找到 Nginx 配置模板"
fi

# 测试 Nginx 配置
echo "8. 测试 Nginx 配置..."
if sudo nginx -t; then
    echo "Nginx 配置测试通过"
else
    echo "错误: Nginx 配置测试失败"
    exit 1
fi

# 重启 Nginx
echo "9. 重启 Nginx..."
sudo systemctl restart nginx

# 检查 Nginx 状态
if systemctl is-active --quiet nginx; then
    echo "Nginx 重启成功"
else
    echo "错误: Nginx 重启失败"
    exit 1
fi

# 创建更新脚本
echo "10. 创建更新脚本..."
cat > /var/www/notes/update-frontend.sh << 'EOF'
#!/bin/bash
# 前端更新脚本

set -e

echo "更新前端..."

# 进入项目目录
cd /path/to/your/project  # 请替换为实际的项目路径

# 拉取最新代码
git pull origin main

# 恢复环境变量文件
if [ -f "/var/www/notes/frontend/.env" ]; then
    cp /var/www/notes/frontend/.env .env
    echo "已恢复环境变量配置"
else
    echo "警告: 未找到环境变量文件，请确保 .env 文件包含正确的 GEMINI_API_KEY"
fi

# 安装依赖
npm install

# 构建项目
npm run build

# 检查构建结果
if [ ! -d "dist" ]; then
    echo "错误: 构建失败，请检查环境变量配置"
    exit 1
fi

# 备份当前版本
if [ -d "/var/www/notes/frontend/dist" ]; then
    sudo mv /var/www/notes/frontend/dist /var/www/notes/frontend/dist.backup.$(date +%Y%m%d_%H%M%S)
fi

# 复制新版本
cp -r dist /var/www/notes/frontend/

# 更新环境变量文件
if [ -f ".env" ]; then
    cp .env /var/www/notes/frontend/
fi

# 设置权限
sudo chown -R nginx:nginx /var/www/notes/frontend/dist
sudo chmod -R 755 /var/www/notes/frontend/dist

# 重启 Nginx
sudo systemctl reload nginx

echo "前端更新完成"
EOF

chmod +x /var/www/notes/update-frontend.sh

echo ""
echo "=== 前端部署完成 ==="
echo ""
echo "部署信息："
echo "- 前端文件位置: $FRONTEND_DIR/dist"
echo "- Nginx 配置: $NGINX_CONF"
echo "- 更新脚本: /var/www/notes/update-frontend.sh"
echo ""
echo "请确保："
echo "1. 域名 DNS 已正确解析到服务器"
echo "2. 防火墙已开放 80 和 443 端口"
echo "3. 如使用 HTTPS，请配置 SSL 证书"
echo ""
echo "访问地址: http://your-domain.com"
