# 知识库搜索功能调试指南

## 问题分析

用户反映点击"语义搜索"、"关键词搜索"按钮没有反应。

## 问题原因

1. **搜索类型按钮的作用**：这些按钮只是切换搜索模式，不会直接执行搜索
2. **正确的搜索方式**：需要在搜索框输入内容，然后按回车键执行搜索

## 调试步骤

### 1. 检查搜索功能是否正常工作

1. 打开知识库面板
2. 点击"语义搜索"标签（左侧导航）
3. 在搜索框中输入一些内容，比如"技术"
4. 按回车键执行搜索
5. 查看是否有搜索结果

### 2. 检查浏览器控制台

打开浏览器开发者工具（F12），查看Console标签：
- 是否有JavaScript错误
- 搜索请求是否发送成功
- API响应是否正常

### 3. 检查网络请求

在开发者工具的Network标签中：
- 查看是否有到 `/api/knowledge/search` 的请求
- 检查请求状态码是否为200
- 查看响应数据是否正确

## 可能的问题和解决方案

### 问题1：API密钥配置
检查 `bankend/.env` 文件中的 `GEMINI_API_KEY` 是否正确配置。

### 问题2：数据库连接
确保数据库连接正常，向量数据存在。

### 问题3：前端状态管理
检查前端状态是否正确更新。

## 测试用例

### 测试1：关键词搜索
1. 选择"关键词搜索"
2. 输入"技术"
3. 按回车
4. 应该返回包含"技术"关键词的笔记

### 测试2：语义搜索
1. 选择"语义搜索"  
2. 输入"编程相关的内容"
3. 按回车
4. 应该返回与编程相关的笔记（即使不包含"编程"这个词）

## 预期行为

- 点击搜索类型按钮：按钮高亮显示，搜索类型切换
- 在搜索框输入内容并按回车：执行搜索，显示结果列表
- 搜索结果：显示匹配的笔记，包括标题、预览、相似度分数等