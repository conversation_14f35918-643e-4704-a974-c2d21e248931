<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能笔记 - 现代化界面演示</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="stylesheet" href="index.css">
    <style>
        /* 演示页面特定样式 */
        .demo-container {
            height: 100vh;
            overflow: hidden;
        }
        
        .demo-note-item {
            opacity: 0;
            animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }
        
        .demo-note-item:nth-child(1) { animation-delay: 0.1s; }
        .demo-note-item:nth-child(2) { animation-delay: 0.2s; }
        .demo-note-item:nth-child(3) { animation-delay: 0.3s; }
        .demo-note-item:nth-child(4) { animation-delay: 0.4s; }
        .demo-note-item:nth-child(5) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="app-container sidebar-mode-full">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <!-- 用户资料 -->
                <div class="sidebar-profile">
                    <div class="avatar">
                        <span class="material-symbols-outlined">person</span>
                    </div>
                    <div class="username">admin</div>
                    <div class="vip-status">💎 VIP</div>
                </div>

                <!-- 新建按钮 -->
                <div class="sidebar-actions">
                    <button class="new-note-btn">
                        <span class="material-symbols-outlined">add_circle</span>
                        新建
                    </button>
                </div>

                <!-- 导航菜单 -->
                <nav class="sidebar-nav">
                    <div class="nav-item active" data-title="最新">
                        <span class="material-symbols-outlined">schedule</span>
                        <span class="item-name">最新</span>
                    </div>
                    
                    <div class="folder-item" data-title="工作">
                        <span class="material-symbols-outlined">work</span>
                        <span class="item-name">工作</span>
                        <span class="folder-arrow material-symbols-outlined">expand_more</span>
                    </div>
                    
                    <div class="folder-item" data-title="学习">
                        <span class="material-symbols-outlined">school</span>
                        <span class="item-name">学习</span>
                        <span class="folder-arrow material-symbols-outlined">expand_more</span>
                    </div>
                    
                    <div class="folder-item" data-title="日常">
                        <span class="material-symbols-outlined">today</span>
                        <span class="item-name">日常</span>
                        <span class="folder-arrow material-symbols-outlined">expand_more</span>
                    </div>
                    
                    <div class="folder-item" data-title="资料">
                        <span class="material-symbols-outlined">folder</span>
                        <span class="item-name">资料</span>
                        <span class="folder-arrow material-symbols-outlined">expand_more</span>
                    </div>
                    
                    <div class="nav-item" data-title="我的资源">
                        <span class="material-symbols-outlined">share</span>
                        <span class="item-name">我的资源</span>
                    </div>
                    
                    <div class="nav-item" data-title="与我分享">
                        <span class="material-symbols-outlined">people</span>
                        <span class="item-name">与我分享</span>
                    </div>
                    
                    <div class="nav-item" data-title="加星">
                        <span class="material-symbols-outlined">star</span>
                        <span class="item-name">加星</span>
                    </div>
                    
                    <div class="nav-item" data-title="回收站">
                        <span class="material-symbols-outlined">delete</span>
                        <span class="item-name">回收站</span>
                    </div>
                </nav>
            </aside>

            <!-- 笔记列表面板 -->
            <div class="note-list-panel">
                <div class="note-list-header">
                    <input type="text" class="search-bar" placeholder="搜索笔记...">
                </div>
                
                <div class="note-list">
                    <div class="note-item demo-note-item active">
                        <h3>项目计划文档</h3>
                        <div class="note-item-snippet">这是一个重要的项目计划文档，包含了详细的时间安排和任务分配...</div>
                        <div class="note-item-meta">2024-01-15 · 工作</div>
                    </div>
                    
                    <div class="note-item demo-note-item">
                        <h3>学习笔记 - React 进阶</h3>
                        <div class="note-item-snippet">深入学习 React 的高级特性，包括 Hooks、Context、性能优化等...</div>
                        <div class="note-item-meta">2024-01-14 · 学习</div>
                    </div>
                    
                    <div class="note-item demo-note-item">
                        <h3>会议记录</h3>
                        <div class="note-item-snippet">今天的团队会议讨论了下个季度的产品规划和技术方向...</div>
                        <div class="note-item-meta">2024-01-13 · 工作</div>
                    </div>
                    
                    <div class="note-item demo-note-item">
                        <h3>读书笔记 - 设计模式</h3>
                        <div class="note-item-snippet">记录了常用的设计模式及其在实际项目中的应用场景...</div>
                        <div class="note-item-meta">2024-01-12 · 学习</div>
                    </div>
                    
                    <div class="note-item demo-note-item">
                        <h3>旅行计划</h3>
                        <div class="note-item-snippet">春节假期的旅行安排，包括行程规划、酒店预订、景点推荐...</div>
                        <div class="note-item-meta">2024-01-11 · 日常</div>
                    </div>
                </div>
            </div>

            <!-- 编辑器面板 -->
            <div class="editor-panel">
                <div class="editor-header">
                    <div class="editor-header-title">
                        <input type="text" class="editor-title-input" value="项目计划文档" placeholder="无标题">
                    </div>
                    <div class="editor-header-actions">
                        <div class="save-status">
                            <span class="material-symbols-outlined">check_circle</span>
                            已保存
                        </div>
                        <button class="btn icon-btn" title="更多选项">
                            <span class="material-symbols-outlined">more_vert</span>
                        </button>
                    </div>
                </div>

                <div class="editor-toolbar">
                    <button class="toolbar-btn" title="撤销">
                        <span class="material-symbols-outlined">undo</span>
                    </button>
                    <button class="toolbar-btn" title="重做">
                        <span class="material-symbols-outlined">redo</span>
                    </button>
                    <div class="toolbar-separator"></div>
                    <button class="toolbar-btn" title="粗体">
                        <span class="material-symbols-outlined">format_bold</span>
                    </button>
                    <button class="toolbar-btn active" title="斜体">
                        <span class="material-symbols-outlined">format_italic</span>
                    </button>
                    <button class="toolbar-btn" title="下划线">
                        <span class="material-symbols-outlined">format_underlined</span>
                    </button>
                    <div class="toolbar-separator"></div>
                    <button class="toolbar-btn" title="标题">
                        <span class="material-symbols-outlined">title</span>
                    </button>
                    <button class="toolbar-btn" title="列表">
                        <span class="material-symbols-outlined">format_list_bulleted</span>
                    </button>
                    <button class="toolbar-btn" title="链接">
                        <span class="material-symbols-outlined">link</span>
                    </button>
                    <div class="toolbar-separator"></div>
                    <button class="toolbar-btn" title="插入图片">
                        <span class="material-symbols-outlined">image</span>
                    </button>
                    <button class="toolbar-btn" title="插入表格">
                        <span class="material-symbols-outlined">table</span>
                    </button>
                </div>

                <div class="editor-content-wrapper">
                    <div class="editor-content" contenteditable="true">
                        <h1>项目计划文档</h1>
                        <p>这是一个现代化的笔记应用界面演示。我们采用了最新的设计趋势和用户体验最佳实践。</p>
                        
                        <h2>主要特性</h2>
                        <ul>
                            <li><strong>现代化设计</strong> - 采用了渐变背景、玻璃态效果和微交互动画</li>
                            <li><strong>响应式布局</strong> - 完美适配桌面端和移动端设备</li>
                            <li><strong>流畅动画</strong> - 丰富的过渡效果和加载动画</li>
                            <li><strong>优雅配色</strong> - 精心调配的色彩方案，提供舒适的视觉体验</li>
                        </ul>
                        
                        <h2>设计亮点</h2>
                        <p>界面采用了卡片式设计，每个元素都有适当的阴影和圆角，营造出层次感。配色方案以现代蓝色为主色调，搭配中性灰色，既专业又不失活力。</p>
                        
                        <blockquote>
                            <p>"好的设计是显而易见的，伟大的设计是透明的。" - Joe Sparano</p>
                        </blockquote>
                        
                        <p>我们相信这个新的界面设计将为用户带来更好的使用体验。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互演示
        document.addEventListener('DOMContentLoaded', function() {
            // 笔记项目点击效果
            const noteItems = document.querySelectorAll('.note-item');
            noteItems.forEach(item => {
                item.addEventListener('click', function() {
                    noteItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 导航项目点击效果
            const navItems = document.querySelectorAll('.nav-item, .folder-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    if (!this.classList.contains('folder-item')) {
                        navItems.forEach(i => i.classList.remove('active'));
                        this.classList.add('active');
                    }
                });
            });
            
            // 工具栏按钮点击效果
            const toolbarBtns = document.querySelectorAll('.toolbar-btn');
            toolbarBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.classList.toggle('active');
                });
            });
        });
    </script>
</body>
</html>
